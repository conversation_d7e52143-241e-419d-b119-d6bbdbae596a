/*
 * 第三方嵌入 SDK 类
 */

// 图标水平方向间隔
const HORIZONTAL_SPACE = 10
// 图标垂直方向间隔
const VERTICAL_SPACE = 10
// 弹窗默认宽度
const POPUP_DEFAULT_WIDTH = 0.8
// 弹窗默认高度
const POPUP_DEFAULT_HEIGHT = 0.9
// 弹窗最小宽度
const POPUP_MIN_WIDTH = 800
// 弹窗最小高度
const POPUP_MIN_HEIGHT = 400
// 形变角度
const SKEW_DGE = 20

export default class AiPopup {
  private visible: boolean
  private bubbleId: string
  private popupId: string | null
  private isDrag: boolean
  private userCode: string

  constructor() {
    this.visible = false
    this.isDrag = true
    this.popupId = null
    this.bubbleId = `bubble_${new Date().getTime()}`
    this.userCode = ''
  }

  // 创建悬浮气泡按钮元素
  createButton(options = {
    userCode: '',
  }) {
    let dragStartTime = 0 // 拖拽开始时间
    let dragEndTime = 0 // 拖拽结束时间
    let dragStartOffset = { x: 0, y: 0 } // 拖拽偏移量
    let dragEndOffset = { x: 0, y: 0 } // 拖拽偏移量
    this.userCode = options.userCode

    const bubble = document.createElement('div')
    bubble.id = this.bubbleId
    bubble.style.position = 'absolute'
    // bubble.style.left = `calc(100vw - 48px - ${HORIZONTAL_SPACE}px)`;
    bubble.style.left = 'calc(100% - 58px)'
    // bubble.style.top = `calc(100vh - 48px - ${VERTICAL_SPACE}px)`;
    bubble.style.top = '100px'
    bubble.style.zIndex = '10086'
    bubble.style.display = 'none'
    bubble.style.alignContent = 'center'
    bubble.style.justifyContent = 'center'
    bubble.style.width = '48px'
    bubble.style.height = '48px'
    bubble.style.color = 'white'
    bubble.style.fontSize = '16px'
    bubble.style.fontWeight = 'bold'
    bubble.style.border = 'none'
    bubble.style.backgroundColor = '#0081CC'
    bubble.style.borderRadius = '50%'
    bubble.style.cursor = 'move'

    const button = document.createElement('button')
    button.textContent = 'AI'
    button.style.color = 'white'
    button.style.fontSize = '16px'
    button.style.fontWeight = 'bold'
    button.style.border = 'none'
    button.style.backgroundColor = 'transparent'
    button.style.borderRadius = '50%'
    button.style.cursor = 'pointer'
    button.style.width = '48px'
    // 添加点击事件处理
    button.addEventListener('click', (e) => {
      if (dragEndTime - dragStartTime > 200)
        return
      if (
        Math.abs(dragStartOffset.x - dragEndOffset.x) > 20
        || Math.abs(dragStartOffset.y - dragEndOffset.y) > 20
      ) return

      e.preventDefault()
      e.stopPropagation()
      this.switchPopups()
    })

    bubble.onmousedown = (e) => {
      dragStartTime = Date.now()
      dragStartOffset = { x: e.clientX, y: e.clientY }

      // 展开状态下禁止拖拽
      if (!this.isDrag)
        return

      button.style.cursor = 'move'

      const offsetX = e.clientX - bubble.offsetLeft
      const offsetY = e.clientY - bubble.offsetTop

      const drag = (event: MouseEvent) => {
        bubble.style.left = `${event.clientX - offsetX}px`
        bubble.style.top = `${event.clientY - offsetY}px`
      }

      const stopDrag = (e: MouseEvent) => {
        dragEndTime = Date.now()
        dragEndOffset = { x: e.clientX, y: e.clientY }
        document.removeEventListener('mousemove', drag)
        document.removeEventListener('mouseup', stopDrag)
        button.style.cursor = 'pointer'
        this.absorbedToSide()
      }

      document.addEventListener('mousemove', drag)
      document.addEventListener('mouseup', stopDrag)
    }

    this.createPopup()
    // this.addHeaderDrag()
    // this.addResize()

    // bubble.appendChild(button)
    // document.body.appendChild(bubble)
  }

  // 开关弹窗
  switchPopups() {
    // const bubble = document.querySelector(`#${this.bubbleId}`) as HTMLDivElement
    if (this.popupId) {
      const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement
      // const clientHeight = document.documentElement.clientHeight
      // const bound = bubble.getBoundingClientRect()
      // const isRightSide = bubble.dataset.side === 'right'
      if (this.visible) {
        this.visible = false
        this.isDrag = true
        // bubble.style.cursor = 'move'
        popup.style.transform = 'scale(1)'
        popup.getBoundingClientRect()

        popup.style.transition = 'all .3s ease-in-out'
        popup.style.transform = 'scale(0)'
        popup.style.top = '5%'
        popup.style.left = '10%'
      }
      else {
        this.visible = true
        this.isDrag = false
        // bubble.style.cursor = 'default'

        // popup.style.marginTop = `-${popup.offsetHeight / 2}px`
        // popup.style.marginLeft = `-${popup.offsetWidth / 2}px`
        // popup.style.top = bubble.style.top
        // popup.style.left = bubble.style.left
        popup.style.top = '5%'
        popup.style.left = '10%'
        popup.style.transform = 'scale(0)'

        popup.getBoundingClientRect()

        popup.style.transition = 'all .3s ease-in-out'
        // popup.style.transform = 'scale(1) translate(-50%, -50%) skew(0deg,0deg)'
        popup.style.transform = 'scale(1)'

        popup.style.top = '5%'
        popup.style.left = '10%'

        // 设置弹窗上、左距离
        // const height = popup.offsetHeight
        // if (bound.top + height < clientHeight)
        //   popup.style.top = `${bound.top}px`
        // else if (bound.top + height > clientHeight)
        //   popup.style.top = `${clientHeight - height - VERTICAL_SPACE}px`

        // if (isRightSide) {
        //   const width = popup.offsetWidth
        //   popup.style.left = `calc(100vw - ${width}px - 80px)`
        // }
        // else {
        //   popup.style.left = '80px'
        // }
      }

      // popup.style.display = this.visible ? 'block' : 'none'
    }
    else {
      this.isDrag = false
      // bubble.style.cursor = 'default'
    }
  }

  // 创建弹窗
  createPopup() {
    this.popupId = `popup_${new Date().getTime()}`
    // 创建弹出页面元素
    const popup = document.createElement('div')
    popup.id = this.popupId
    popup.style.position = 'fixed'
    popup.style.top = '100vh'
    popup.style.left = '5%'
    popup.style.zIndex = '10000'
    popup.style.width = `calc(${POPUP_DEFAULT_WIDTH} * 100vw)`
    popup.style.height = `calc(${POPUP_DEFAULT_HEIGHT} * 100vh)`
    popup.style.backgroundColor = 'white'
    popup.style.border = '1px solid #DFE4E8'
    popup.style.borderRadius = '10px'
    popup.style.boxShadow = '0 0 20px 0 #434b5b26'
    // popup.style.transform = `scale(0) skew(${SKEW_DGE}deg,${SKEW_DGE}deg)`
    popup.style.transformOrigin = 'center center'

    const iframe = document.createElement('iframe')
    iframe.style.width = '100%'
    iframe.style.height = '100%'
    iframe.style.border = 'none'
    iframe.style.borderRadius = '10px'
    let origin = 'https://news-craft-test.coli688.com'
    if (process.env.NEXT_PUBLIC_DEPLOY_ENV === 'PRODUCTION')
      origin = 'https://news-craft.coli688.com'
    iframe.src = encodeURI(`${origin}/#/creation/index?mini=1&code=${this.userCode}`)
    popup.appendChild(iframe)

    // 添加关闭按钮
    const closeButton = document.createElement('button')
    closeButton.style.position = 'absolute'
    closeButton.style.width = '18px'
    closeButton.style.height = '18px'
    closeButton.style.right = '24px'
    closeButton.style.top = '24px'
    closeButton.style.border = 'none'
    closeButton.style.backgroundColor = 'transparent'
    closeButton.style.color = '#A3AFBB'
    closeButton.style.cursor = 'pointer'
    closeButton.style.zIndex = '10'
    closeButton.innerHTML = '&#x2715;'

    closeButton.addEventListener('click', () => {
      this.switchPopups()
    })

    popup.appendChild(closeButton)

    // 添加其他内容到弹出页面
    document.body.appendChild(popup)

    // 关闭动画结束重置样式
    popup.addEventListener('transitionend', () => {
      // popup.style.display = this.visible ? 'block' : 'none'
      // 弹窗动画结束时移除动画效果
      popup.style.transition = 'unset'
      // if (!this.visible) {
      //   popup.style.top = '100vh'
      //   popup.style.left = '100vw'
      // }
      if (this.visible)
        popup.style.transform = 'unset'
    })
  }

  removePopup() {
    const popupEl = document.getElementById(this.popupId || '')
    if (popupEl)
      document.body.removeChild(popupEl)
  }

  // 吸附到边缘
  absorbedToSide() {
    const bubble = document.querySelector(`#${this.bubbleId}`) as HTMLDivElement
    const bound = bubble.getBoundingClientRect()
    const clientWidth = document.documentElement.clientWidth
    const clientHeight = document.documentElement.clientHeight

    const left = bound.left
    const right = clientWidth - bound.right
    const top = bound.top

    // 左右吸边
    if (left > right) {
      bubble.style.left = `calc(100vw - ${HORIZONTAL_SPACE}px - ${bound.width}px)`
      bubble.dataset.side = 'right'
    }
    else {
      bubble.style.left = `${HORIZONTAL_SPACE}px`
      bubble.dataset.side = 'left'
    }

    // 上下边界判定
    if (top < 0)
      bubble.style.top = '10px'
    else if (top > clientHeight - bound.height)
      bubble.style.top = `calc(100vh - ${bound.height}px - ${VERTICAL_SPACE}px)`
    else
      bubble.style.top = `${top}px`
  }

  // 头部拖拽移动
  addHeaderDrag() {
    const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement
    const div = document.createElement('div')
    div.classList.add('drag-header')

    div.style.height = '62px'
    div.style.width = '100%'
    div.style.position = 'absolute'
    div.style.top = '0px'
    div.style.left = '0px'
    div.style.cursor = 'move'
    div.style.userSelect = 'none'

    let mouseOffset = { x: 0, y: 0 }
    let popupOffset = { x: 0, y: 0 }
    const mousedown = (e: MouseEvent) => {
      const bound = popup.getBoundingClientRect()
      mouseOffset = {
        x: e.clientX,
        y: e.clientY,
      }
      popupOffset = {
        x: bound.left,
        y: bound.top,
      }

      const mousemove = (e: MouseEvent) => {
        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`
        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`
      }
      const mouseup = () => {
        div.removeEventListener('mouseup', mouseup)
        document.removeEventListener('mousemove', mousemove)
      }

      const mouseleave = () => {
        document.removeEventListener('mousemove', mousemove)
      }

      div.addEventListener('mouseup', mouseup)
      document.addEventListener('mousemove', mousemove)
      document.addEventListener('mouseleave', mouseleave)
    }

    div.addEventListener('mousedown', mousedown)
    popup.appendChild(div)
  }

  // 添加拉伸功能
  addResize() {
    this.addTopEdgeResize()
    this.addBottomEdgeResize()
    this.addLeftEdgeResize()
    this.addRightEdgeResize()
    this.addTopLeftCornerResize()
    this.addTopRightCornerResize()
    this.addBottomLeftCornerResize()
    this.addBottomRightCornerResize()
  }

  createResizeEdge({ style, onmousemove }: { [x: string]: any; onmousemove: Function }) {
    const popup = document.querySelector(`#${this.popupId}`) as HTMLDivElement
    const edgeEl = document.createElement('div')
    const iframe = popup.querySelector('iframe') as HTMLIFrameElement
    const dragHeader = popup.querySelector('.drag-header') as HTMLIFrameElement

    for (const [key, value] of Object.entries(style))
      edgeEl.style[key as any] = value as string

    let mouseOffset = { x: 0, y: 0 }
    let popupOffset = { x: 0, y: 0 }
    let popupWidth = 0
    let popupHeight = 0

    const mousedown = (e: MouseEvent) => {
      const bound = popup.getBoundingClientRect()
      iframe.style.pointerEvents = 'none'
      dragHeader.style.pointerEvents = 'none'

      mouseOffset = {
        x: e.clientX,
        y: e.clientY,
      }
      popupOffset = {
        x: bound.left,
        y: bound.top,
      }
      popupHeight = bound.height
      popupWidth = bound.width

      const mousemove = (e: MouseEvent) => {
        onmousemove?.({ popup, popupOffset, mouseOffset, popupHeight, popupWidth, e })
      }
      const mouseup = () => {
        iframe.style.pointerEvents = 'all'
        dragHeader.style.pointerEvents = 'all'
        document.removeEventListener('mouseup', mouseup)
        document.removeEventListener('mousemove', mousemove)
      }

      document.addEventListener('mouseup', mouseup)
      document.addEventListener('mousemove', mousemove)
    }

    edgeEl.addEventListener('mousedown', mousedown)
    popup.appendChild(edgeEl)
  }

  addTopEdgeResize() {
    const style = {
      position: 'absolute',
      top: '0',
      left: '0',
      width: '100%',
      height: '4px',
      cursor: 'ns-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, popupOffset, mouseOffset, popupHeight, e }: { [x: string]: any }) => {
      const height = popupHeight + mouseOffset.y - e.clientY

      if (height < POPUP_MIN_HEIGHT)
        return

      popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`
      popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addBottomEdgeResize() {
    const style = {
      position: 'absolute',
      bottom: '0',
      left: '0',
      width: '100%',
      height: '4px',
      cursor: 'ns-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupHeight, e }: { [x: string]: any }) => {
      const height = popupHeight + e.clientY - mouseOffset.y

      if (height < POPUP_MIN_HEIGHT)
        return

      popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addLeftEdgeResize() {
    const style = {
      position: 'absolute',
      bottom: '0',
      left: '0',
      width: '4px',
      height: '100%',
      cursor: 'ew-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, e }: { [x: string]: any }) => {
      const width = popupWidth + mouseOffset.x - e.clientX

      if (width < POPUP_MIN_WIDTH)
        return

      popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`
      popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addRightEdgeResize() {
    const style = {
      position: 'absolute',
      bottom: '0',
      right: '0',
      width: '4px',
      height: '100%',
      cursor: 'ew-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupWidth, e }: { [x: string]: any }) => {
      const width = popupWidth + e.clientX - mouseOffset.x

      if (width < POPUP_MIN_WIDTH)
        return

      popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addTopLeftCornerResize() {
    const style = {
      position: 'absolute',
      top: '0',
      left: '0',
      width: '10px',
      height: '10px',
      cursor: 'nw-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {
      const height = popupHeight + mouseOffset.y - e.clientY
      const width = popupWidth + mouseOffset.x - e.clientX
      if (height >= POPUP_MIN_HEIGHT) {
        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`
        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
      }
      if (width > POPUP_MIN_WIDTH) {
        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`
        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`
      }
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addTopRightCornerResize() {
    const style = {
      position: 'absolute',
      top: '0',
      right: '0',
      width: '10px',
      height: '10px',
      cursor: 'ne-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {
      const height = popupHeight + mouseOffset.y - e.clientY
      const width = popupWidth + e.clientX - mouseOffset.x
      if (height >= POPUP_MIN_HEIGHT) {
        popup.style.top = `${popupOffset.y + e.clientY - mouseOffset.y}px`
        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
      }
      if (width >= POPUP_MIN_WIDTH)
        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addBottomLeftCornerResize() {
    const style = {
      position: 'absolute',
      bottom: '0',
      left: '0',
      width: '10px',
      height: '10px',
      cursor: 'sw-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {
      const width = popupWidth + mouseOffset.x - e.clientX
      const height = popupHeight + e.clientY - mouseOffset.y
      if (width >= POPUP_MIN_WIDTH) {
        popup.style.left = `${popupOffset.x + e.clientX - mouseOffset.x}px`
        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`
      }
      if (height >= POPUP_MIN_HEIGHT)
        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }

  addBottomRightCornerResize() {
    const style = {
      position: 'absolute',
      bottom: '0',
      right: '0',
      width: '10px',
      height: '10px',
      cursor: 'se-resize',
      userSelect: 'none',
    }
    const onmousemove = ({ popup, mouseOffset, popupWidth, popupHeight, e }: { [x: string]: any }) => {
      const width = popupWidth + e.clientX - mouseOffset.x
      const height = popupHeight + e.clientY - mouseOffset.y
      // eslint-disable-next-line no-self-compare
      if (width >= width)
        popup.style.width = `${width < POPUP_MIN_WIDTH ? POPUP_MIN_WIDTH : width}px`

      if (height >= POPUP_MIN_HEIGHT)
        popup.style.height = `${height < POPUP_MIN_HEIGHT ? POPUP_MIN_HEIGHT : height}px`
    }
    this.createResizeEdge({ style, onmousemove })
  }
}
