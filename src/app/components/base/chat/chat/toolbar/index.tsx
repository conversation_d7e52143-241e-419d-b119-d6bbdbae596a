import React, { useEffect, useRef, useState } from 'react'
import { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE, INTENT_PPT } from '../u-const'
import { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '../../../portal-to-follow-elem'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import MySDK from './sdks/index'
import type AiPopup from './sdks/index'
import { ENTERPRISE_NAME } from '@/config/index'

type SvgProps = {
  className?: string
}
type ToolbarProps = {
  label: string
  icon: React.JSX.Element
  prompt: string
  style?: string
  link?: string
  type?: string
}

const IconTalk = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M261.3248 877.9264c-54.3744 43.52-64.9216 50.432-90.624 50.4832-23.8592 0-46.4896-10.8544-61.3888-29.5424-16.0256-20.0704-17.152-32.6144-17.152-102.2464V307.2c0-66.9696 1.28-82.8416 13.2096-106.24a121.1392 121.1392 0 0 1 52.9408-52.9408c23.3984-11.8784 39.2704-13.2096 106.24-13.2096h494.9504c66.9184 0 82.7904 1.3312 106.1376 13.2096 22.8352 11.6224 41.4208 30.208 52.992 52.9408 11.9296 23.3472 13.2096 39.2704 13.2096 106.0864v324.608c0 66.7648-1.28 82.688-13.2096 105.984-11.6224 22.8352-30.1568 41.3696-52.9408 52.992-23.3472 11.9296-39.2704 13.2096-106.0864 13.2096h-376.32c-14.4384 0-18.3296 0.256-21.9648 1.024a49.664 49.664 0 0 0-12.5952 4.4032l-1.024 0.512c-3.4816 2.048-7.168 4.7616-21.504 16.2304l-64.8704 51.9168z m20.6848-108.3392c17.3056-13.824 22.1696-17.5104 30.4128-22.1696l3.7376-1.9968a121.344 121.344 0 0 1 30.72-10.752c10.8544-2.2528 17.3568-2.5088 42.3424-2.5088h380.3648c44.0832-0.1536 55.552-1.3312 63.5392-5.376a49.5104 49.5104 0 0 0 21.6576-21.6576c4.352-8.5504 5.376-21.0944 5.376-73.5232V307.0464c0-52.4288-1.024-64.9728-5.376-73.5232a49.5616 49.5616 0 0 0-21.6576-21.6064c-8.6016-4.4032-21.1456-5.4272-73.6256-5.4272H254.5664c-44.2368 0.1536-55.7056 1.3312-63.6928 5.4272a49.4592 49.4592 0 0 0-21.6576 21.6064c-4.352 8.5504-5.376 21.1456-5.376 73.728v497.7664c0.1536 37.376 1.2288 48.7936 1.536 49.152 1.28 1.6384 3.2256 2.56 5.2736 2.56 0.512 0 11.52-7.2704 45.8752-34.816l65.536-52.3264z" p-id="1001"></path><path d="M341.3504 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" p-id="1002"></path><path d="M512 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" p-id="1003"></path><path d="M682.6496 469.3504m-51.2 0a51.2 51.2 0 1 0 102.4 0 51.2 51.2 0 1 0-102.4 0Z" p-id="1004"></path></svg>
)
const IconSchedule = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M716.8 66.56a35.84 35.84 0 0 1 35.84 35.84l-0.0512 55.1424h0.6144c66.7136 0 85.5552 1.28 107.9296 11.776l4.2496 2.048c23.8592 12.1856 43.3152 31.5904 55.4496 55.4496 12.4416 24.4224 13.824 41.2672 13.824 112.128v449.1776c-0.2048 60.8256-2.0992 77.056-13.824 100.0448a126.9248 126.9248 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.2672 13.824-112.0256 13.824h-437.248c-70.8096 0-87.6544-1.3824-112.0256-13.824a126.8736 126.8736 0 0 1-55.4496-55.4496c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V339.0976c0-70.9632 1.3824-87.808 13.824-112.2816a126.8224 126.8224 0 0 1 55.4496-55.3984c24.4224-12.4416 41.2672-13.824 112.1792-13.824l0.512-0.0512L316.8768 102.4a35.84 35.84 0 0 1 30.976-35.5328l4.864-0.3072a35.84 35.84 0 0 1 35.84 35.84v55.1424h292.352L680.96 102.4a35.84 35.84 0 0 1 30.976-35.5328L716.8 66.56z m146.1248 344.7296H206.4896v374.784c0.1536 44.9024 1.2288 58.7264 5.12 67.6864l0.8704 1.8432c5.3248 10.4448 13.7728 18.8928 24.1152 24.1664 9.6256 4.864 23.1424 5.9904 79.5648 5.9904h437.1968c56.3712 0 69.8368-1.1264 79.4624-5.9904 10.3936-5.3248 18.8416-13.7728 24.1664-24.1664 4.864-9.5744 5.9904-23.04 5.9904-79.4624l-0.0512-364.8512z m-328.192 87.6032a35.84 35.84 0 0 1 35.84 35.84l-0.0512 77.9264h77.9776a35.84 35.84 0 0 1 35.5328 31.0272l0.3072 4.864a35.84 35.84 0 0 1-35.84 35.84l-77.9776-0.0512v77.9776a35.84 35.84 0 0 1-30.9248 35.4816l-4.864 0.3584a35.84 35.84 0 0 1-35.84-35.84l-0.0512-77.9776H420.9664a35.84 35.84 0 0 1-35.5328-30.9248l-0.3072-4.864a35.84 35.84 0 0 1 35.84-35.84l77.824-0.0512 0.1024-77.9264a35.84 35.84 0 0 1 30.976-35.4816l4.864-0.3584zM352.6656 229.2736h-36.352c-56.5248 0-70.0928 1.0752-79.7184 5.9904a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9664 9.6256-6.0416 23.1424-6.0416 79.6672v0.5632h656.4864v-0.7168c0-52.8384-0.9728-68.0448-5.12-77.6704l-0.8704-1.8432a55.1936 55.1936 0 0 0-24.1664-24.064c-9.6256-4.9664-23.1424-6.0416-79.616-6.0416H352.7168z" p-id="1161"></path></svg>
)
const IconLog = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M593.92 66.56h4.3008l10.4448 0.0512c14.336 0.1024 23.4496 0.6656 32.256 2.304l5.2224 1.1264a126.8224 126.8224 0 0 1 42.496 19.0976c9.0624 6.4 14.4896 11.4688 33.5872 30.5664l142.2336 142.2848c21.8624 21.8624 27.392 27.904 34.4064 39.424 6.9632 11.3152 12.1344 23.7056 15.2064 36.608 2.56 10.5984 3.328 20.3776 3.4816 37.0688l0.0512 10.9056V776.1408c0 70.8096-1.3824 87.6032-13.824 112.0256a126.976 126.976 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.2672 13.824-112.0768 13.824H563.2a35.84 35.84 0 0 1 0-71.68h173.056c56.4224 0 69.888-1.1264 79.5136-5.9904a55.296 55.296 0 0 0 24.1664-24.1664c4.864-9.5744 5.9904-23.04 5.9904-79.4624V411.2896h-103.68c-57.088-0.1536-74.9568-1.8432-95.8976-11.7248l-4.2496-2.048A126.8736 126.8736 0 0 1 586.752 342.016c-12.4416-24.4224-13.824-41.2672-13.824-112.1792L572.8256 138.24H380.16c-44.9024 0.1536-58.7264 1.2288-67.7376 5.12l-1.8432 0.8704a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9664 9.6256-6.0416 23.1936-6.0416 79.7184V466.432a35.84 35.84 0 0 1-71.68 0V248.064c0-70.9632 1.3824-87.808 13.824-112.2304A126.8224 126.8224 0 0 1 278.016 80.384c24.4736-12.4416 41.3184-13.824 112.2304-13.824H593.92zM469.6064 441.1392a35.84 35.84 0 0 1 50.688 0l113.7664 113.7664a35.84 35.84 0 0 1 0 50.688l-341.3504 341.3504a35.84 35.84 0 0 1-25.344 10.496H153.6a35.84 35.84 0 0 1-35.84-35.84v-113.7664a35.84 35.84 0 0 1 10.496-25.344l341.3504-341.3504z m-99.84 201.1648L189.44 822.5792v63.1296h63.1296l180.2752-180.3264-63.0784-63.0784z m125.1328-125.1328l-74.4448 74.4448 63.0784 63.0784 74.4448-74.4448-63.0784-63.0784z m150.4768-370.7904l-0.8704-0.512v83.968c0 52.992 1.024 68.1472 5.1712 77.824l0.8704 1.8432c5.3248 10.3936 13.7728 18.8416 24.1152 24.1152 9.6256 4.9152 23.1424 5.9904 79.5136 5.9904h84.0192l-0.4096-0.768c-2.56-4.1472-5.9392-7.9872-18.7392-20.8896l-5.2736-5.2736-142.2336-142.2848-8.9088-8.8064a130.4064 130.4064 0 0 0-15.4112-13.9776l-1.8432-1.2288z" p-id="1011"></path></svg>
)
const IconSystem = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M727.04 117.76c69.9392 0 86.528 1.3312 110.6944 13.6704 23.6032 12.032 42.8032 31.232 54.8352 54.784 12.288 24.1664 13.6704 40.7552 13.6704 110.592V422.4a35.84 35.84 0 0 1-71.68 0V296.8064c0-55.3984-1.0752-68.6592-5.8368-78.0288a53.76 53.76 0 0 0-23.552-23.5008c-9.3696-4.7616-22.6304-5.8368-78.1312-5.8368H287.0272c-47.2064 0.1536-59.4432 1.3824-68.2496 5.8368-10.0864 5.1712-18.3296 13.4144-23.5008 23.552-4.7616 9.3184-5.8368 22.6304-5.8368 78.1312v440.0128c0.1536 47.2576 1.3824 59.4432 5.8368 68.2496 5.1712 10.1376 13.4144 18.3296 23.552 23.5008 9.3184 4.7616 22.528 5.8368 77.9776 5.8368H422.4a35.84 35.84 0 0 1 0 71.68H296.8064c-69.8368 0-86.4256-1.3824-110.592-13.6704a125.44 125.44 0 0 1-54.784-54.784c-12.288-24.2176-13.6704-40.8064-13.6704-110.7456V296.96c0-69.9392 1.3824-86.528 13.6704-110.6944 12.032-23.6032 31.232-42.8032 54.784-54.8352 24.2176-12.288 40.8064-13.6704 110.7456-13.6704h430.08z m-147.8144 268.8a192.6144 192.6144 0 0 1 159.744 300.3392l-0.6144 0.8192 112.5888 112.5376a35.84 35.84 0 0 1 3.7888 46.2336l-3.7888 4.4544a35.84 35.84 0 0 1-50.688 0l-112.5376-112.5888-0.8192 0.5632a191.5904 191.5904 0 0 1-97.792 32.6656l-9.9328 0.256a192.6144 192.6144 0 1 1 0-385.28z m0 71.68a120.9344 120.9344 0 1 0 0 241.92 120.9344 120.9344 0 0 0 0-241.92z" p-id="1168"></path></svg>
)
const IconImage = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M765.44 117.76c69.9392 0 86.528 1.3312 110.6944 13.6704 23.6032 12.032 42.8032 31.232 54.8352 54.784 12.288 24.1664 13.6704 40.7552 13.6704 110.592v430.3872c0 31.0272-0.1024 41.9328-0.8704 54.6304-1.3312 23.5008-4.864 40.2944-12.8 55.9104-12.032 23.552-31.232 42.8032-54.784 54.8352-24.1664 12.288-40.7552 13.6704-110.592 13.6704H245.6064c-69.8368 0-86.4256-1.3824-110.592-13.6704a125.44 125.44 0 0 1-54.784-54.8352c-11.776-23.1936-13.6192-42.2912-13.6704-101.6832l0.0512 0.6656-0.0512-0.7168V296.96c0-69.9392 1.3824-86.528 13.6704-110.6944 12.032-23.6032 31.232-42.8032 54.784-54.8352 24.2176-12.288 40.8064-13.6704 110.7456-13.6704h519.68zM342.8864 510.464L138.24 749.1584l0.1024 4.864c0.3584 29.696 1.6384 41.5744 4.864 49.152l0.8704 2.048c5.1712 10.0864 13.4144 18.3296 23.552 23.5008 9.3184 4.7616 22.528 5.8368 77.9776 5.8368h519.9872c55.3984 0 68.608-1.0752 78.0288-5.8368 10.0864-5.12 18.3296-13.4144 23.5008-23.552a38.1952 38.1952 0 0 0 3.2256-10.24l0.6144-4.0448-119.296-149.1968c-15.2064-18.944-20.3264-24.6272-21.9648-25.856a8.96 8.96 0 0 0-6.2976-0.5632 7.8848 7.8848 0 0 0-1.9968 1.3312l-2.9184 2.56c-3.6864 3.328-9.8816 9.2672-20.1728 19.6096l-28.672 28.672c-24.3712 24.064-31.9488 29.7984-48.64 34.7136-17.2544 5.12-35.7376 4.3008-52.5312-2.3552-17.408-6.912-24.2688-13.824-50.8416-45.7216L397.9776 510.464c-18.0736-21.6576-23.04-26.624-24.1664-27.0848a8.96 8.96 0 0 0-6.0928 0 6.9632 6.9632 0 0 0-1.6896 1.3312l-2.4576 2.4576c-3.584 3.7376-10.0864 10.9056-21.4016 24.064l0.7168-0.768z m432.4864-321.024H235.8272c-47.2064 0.1536-59.4432 1.3824-68.2496 5.8368-10.0864 5.1712-18.3296 13.4144-23.5008 23.552-4.7616 9.3184-5.8368 22.6304-5.8368 78.1312v342.1696l151.6544-176.896-1.024 1.1776c29.0304-33.8944 36.3008-41.0112 54.9376-47.616 17.7664-6.2976 37.2224-6.144 54.9376 0.3584 18.4832 6.8608 25.7024 14.1312 54.272 48.4352l119.6544 143.616c10.6496 12.7488 16.3328 19.2 19.3024 22.3744l1.9968 1.8944c0.4608 0.4096 0.768 0.6656 0.9216 0.7168a8.96 8.96 0 0 0 5.8368 0.2048c1.1264-0.3072 6.0416-4.5056 24.6272-23.04l22.272-22.272c30.0032-30.0544 37.5808-36.4032 55.9104-41.6768a80.6912 80.6912 0 0 1 53.1456 2.9696c17.6128 7.2704 24.4736 14.4896 50.944 47.5648l65.3312 81.664V296.8064c0-51.9168-0.9728-66.8672-5.0176-76.2368l-0.8192-1.792a53.76 53.76 0 0 0-23.552-23.5008c-9.3184-4.7616-22.6304-5.8368-78.1312-5.8368h9.9328zM640 296.96a80.64 80.64 0 1 1 0 161.28 80.64 80.64 0 0 1 0-161.28z" p-id="1325"></path></svg>

)
const IconInternet = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M512 66.56l13.3632 0.2048A445.44 445.44 0 1 1 66.56 512l0.2048-13.3632A445.44 445.44 0 0 1 512 66.56z m145.408 481.28H366.5408l0.3584 8.192c9.1136 182.528 74.0352 322.4064 139.8784 329.472L512 885.76l5.2224-0.256c65.8432-7.0656 130.7648-146.944 139.8784-329.5232l0.3072-8.1408z m-362.6496 0H139.9296l0.1024 0.8704a374.0672 374.0672 0 0 0 231.8848 309.9136l0.4096 0.1536-2.2016-3.7376c-42.3424-72.96-69.4784-178.8928-74.9568-297.8816l-0.4096-9.3184z m589.2608 0h-154.8288l-0.3584 9.3184c-5.4784 118.9888-32.6144 224.9216-74.9568 297.8816l-2.2528 3.7376a374.1696 374.1696 0 0 0 232.3456-310.0672l0.0512-0.8704z m-232.3968-382.6688l2.2528 3.7888c42.3424 72.96 69.4784 178.8928 74.9568 297.8816l0.3584 9.3184h154.8288v-0.8704a374.0672 374.0672 0 0 0-231.936-309.9136l-0.4608-0.2048z m-144.8448-26.624c-65.8432 7.0144-130.7648 146.944-139.8784 329.472l-0.3584 8.1408h290.8672l-0.3072-8.192c-9.1136-182.528-74.0352-322.4064-139.8784-329.472L512 138.24l-5.2224 0.256z m-134.4512 26.624a374.3744 374.3744 0 0 0-232.2944 310.1184l-0.1024 0.8704h154.8288l0.4096-9.3184c5.4784-118.9888 32.6144-224.9216 74.9568-297.8816l2.2016-3.7888z" p-id="1482"></path></svg>
)
const IconProcess = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M373.76 476.16v71.68H307.2A66.56 66.56 0 0 0 240.64 614.4v93.0816a66.56 66.56 0 0 0 66.56 66.56h66.56v71.68H307.2a138.24 138.24 0 0 1-138.24-138.24V614.4A138.24 138.24 0 0 1 307.2 476.16h66.56z m358.4 297.8304l130.56 0.0512a35.84 35.84 0 0 1 0 71.68H732.16v-71.68z m28.16-595.712a138.24 138.24 0 0 1 138.24 138.24V409.6a138.24 138.24 0 0 1-138.24 138.24h-28.16V476.16h28.16a66.56 66.56 0 0 0 66.56-66.56V316.5184a66.56 66.56 0 0 0-66.56-66.56h-28.16v-71.68h28.16z m-386.56 71.68H204.8a35.84 35.84 0 1 1 0-71.68h168.96v71.68z" p-id="1639"></path><path d="M651.6224 102.4a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 1 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 1 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768zM651.6224 400.2816a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 0 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 1 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768zM651.6224 698.1632a111.7184 111.7184 0 0 1 0 223.4368H428.2368a111.7184 111.7184 0 0 1 0-223.4368h223.3856z m0 71.68H428.2368a40.0384 40.0384 0 0 0 0 80.0768h223.3856a40.0384 40.0384 0 1 0 0-80.0768z" p-id="1640"></path></svg>
)
const IconPPT = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" ><path d="M554.0864 66.56h4.3008l10.496 0.0512c14.2848 0.1024 23.3984 0.6656 32.1536 2.3552l5.2736 1.1264c12.9536 3.072 25.344 8.192 36.6592 15.1552 3.0208 1.8944 3.0208 1.8944 5.888 3.8912 9.0624 6.4 14.4384 11.52 33.536 30.5664l148.3264 148.3776c16.896 16.9984 22.016 22.9376 28.3648 33.28 6.9632 11.3664 12.0832 23.7568 15.2064 36.6592 2.56 10.5984 3.328 20.4288 3.4816 37.12v412.928c-0.1536 60.8768-2.048 77.1072-13.824 100.096a126.976 126.976 0 0 1-55.4496 55.4496c-24.4224 12.4416-41.216 13.824-112.0256 13.824H350.3104c-70.8608 0-87.6544-1.3824-112.128-13.824a126.8736 126.8736 0 0 1-55.3984-55.4496c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V248.064c0-70.9632 1.3824-87.808 13.824-112.2304A126.8224 126.8224 0 0 1 238.2336 80.384c24.4224-12.4416 41.2672-13.824 112.2304-13.824h203.6224z m-21.0432 71.68H340.3264c-48.128 0.1536-60.5184 1.4336-69.5808 5.9904a55.1424 55.1424 0 0 0-24.064 24.1152c-4.9152 9.6256-6.0416 23.1936-6.0416 79.7184v538.0096c0.1536 48.128 1.4336 60.5184 5.9904 69.5296 5.3248 10.4448 13.7728 18.8928 24.1152 24.1664 9.6256 4.864 23.1424 5.9904 79.5648 5.9904h346.112c56.4224 0 69.9392-1.1264 79.5136-5.9904a55.296 55.296 0 0 0 24.1664-24.1664c4.9152-9.5744 5.9904-23.04 5.9904-79.4624V411.2896h-103.68c-57.0368-0.1536-74.9056-1.8432-95.8464-11.7248l-4.2496-2.048A126.8736 126.8736 0 0 1 546.816 342.016c-12.4416-24.4224-13.824-41.2672-13.824-112.1792V138.24z m72.4992 8.1408l-0.8192-0.4608v94.0032c0.1536 44.9024 1.2288 58.7264 5.12 67.7376l0.9216 1.8432c5.2736 10.3936 13.7216 18.8416 24.064 24.1152 9.6256 4.9152 23.1424 5.9904 79.5648 5.9904h84.0192l-0.4608-0.768c-2.56-4.1472-5.888-7.9872-18.7392-20.8896l-152.32-152.32c-11.776-11.776-15.6672-15.3088-19.456-18.0224l-1.8944-1.2288z" p-id="1031"></path><path d="M539.904 675.9424H477.5936v99.7376c0 14.2848-3.1744 25.088-9.6256 32.4608a30.7712 30.7712 0 0 1-24.2688 11.0592c-10.24 0-18.432-3.6352-24.7296-10.9568-6.2464-7.2704-9.3696-17.92-9.3696-32.0512V504.5248c0-15.6672 3.4304-26.88 10.2912-33.6384 6.8608-6.7072 17.8176-10.0864 32.768-10.0864h87.2448c25.8048 0 45.6192 2.048 59.5456 6.2464a87.3984 87.3984 0 0 1 58.368 54.0672c5.1712 13.4656 7.7824 28.672 7.7824 45.4656 0 35.84-10.5472 63.0784-31.5904 81.6128-21.0944 18.4832-52.4288 27.7504-94.1056 27.7504z m-16.4864-161.536h-45.824v107.6736h45.824c16.0256 0 29.3888-1.7408 40.192-5.2736a46.7968 46.7968 0 0 0 24.576-17.3056c5.632-8.0384 8.4992-18.5344 8.4992-31.488 0-15.5648-4.352-28.2112-13.056-37.9904-9.728-10.3936-29.8496-15.616-60.2112-15.616z" p-id="1032"></path></svg>
)

const IconDocumentAnalysis = ({ className }: SvgProps) => (
  <svg className={className} fill='currentColor' viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
    <path d="M842.667 981.333H181.333A53.393 53.393 0 0 1 128 928V96a53.393 53.393 0 0 1 53.333-53.333H648.08a52.987 52.987 0 0 1 37.713 15.62L880.38 252.873A52.987 52.987 0 0 1 896 290.587V928a53.393 53.393 0 0 1-53.333 53.333z m-661.334-896A10.667 10.667 0 0 0 170.667 96v832a10.667 10.667 0 0 0 10.666 10.667h661.334A10.667 10.667 0 0 0 853.333 928V298.667h-160A53.393 53.393 0 0 1 640 245.333v-160zM682.667 115.5v129.833A10.667 10.667 0 0 0 693.333 256h129.834zM704 768H320a21.333 21.333 0 0 1 0-42.667h384A21.333 21.333 0 0 1 704 768z m0-213.333H320A21.333 21.333 0 0 1 320 512h384a21.333 21.333 0 0 1 0 42.667z m-213.333-256H320A21.333 21.333 0 0 1 320 256h170.667a21.333 21.333 0 0 1 0 42.667z"></path>
  </svg>
)
const IconAll = ({ className }: SvgProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <path d="M17.4,10 L10.2,14.8 L3,10 M17.4,13.2 L10.2,18 L3,13.2 M17.4,6.8 L10.2,11.6 L3,6.8 L10.2,2 L17.4,6.8 Z" stroke="#747E8C" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd" />
  </svg>
)
const SvgClass = 'w-[20px] h-[20px] mr-[4px]'
const defaultToolList = [
  {
    label: '自由对话',
    icon: <IconTalk className={SvgClass} />,
    prompt: '欢迎使用小海AI! 您可以直接向我提问。在自由对话场景下，可以选择使用不同的模型进行问答，您可以通过对话框左侧的模型切换按钮进行不同模型的选择',
  },
  {
    label: '文档分析',
    icon: <IconDocumentAnalysis className={SvgClass} />,
    prompt: '您可以上传文档或图片，并基于文档或图片的内容向我提问。文档分析服务使用中海本地部署的私有化大模型，信息安全有保障。目前暂不支持扫描类PDF文档。',
  },
  {
    label: '查制度',
    icon: <IconSystem className={SvgClass} />,
    prompt: `您可以向我提问有关${ENTERPRISE_NAME}集团的制度，比如“如何申请使用合同专用章？”“与合约管理有关的制度文件”，目前我支持【综合管理】、【成本管理】等制度内容的查询，制度类型持续拓展中。`,
  },
  {
    label: 'PPT生成(内测)',
    icon: <IconPPT className={SvgClass} />,
    prompt: '您可以向我提出PPT的主题、要求、期望的页数范围，我将为您生成PPT大纲，您可以对我生成的大纲提出调整优化的要求。在您确认大纲后，我将为您生成最终的PPT文件',
  },
  {
    label: '订日程',
    icon: <IconSchedule className={SvgClass} />,
    prompt: '我可以帮您创建日程，您可以通过下面类似的问法向我交办任务：“预定明天上午10点到11点的日程，主题是项目汇报”',
  },
  {
    label: '写日志',
    icon: <IconLog className={SvgClass} />,
    prompt: '我可以帮您随手记录当天日志，并存储至OA工作日志系统中。您可以直接向我发送需要记录的日志内容',
  },
  {
    label: '图片生成',
    icon: <IconImage className={SvgClass} />,
    prompt: '我可以帮您生成图片，您可以向我描述想要的图片效果，比如”帮我生成一张摩天大楼的图片”',
  },
  {
    label: '联网检索',
    icon: <IconInternet className={SvgClass} />,
    prompt: '您可以直接向我提出需要联网检索的内容，我将实时联网检索，并将检索内容进行总结，为您提供高效的检索体验。',
  },
  {
    label: '查流程',
    icon: <IconProcess className={SvgClass} />,
    prompt: '我可以帮您查询个人名下的OA流程，您可以这样查询“帮我查询我1月份发起的流程”、“我当前待办的流程有哪些”',
  },
]

// 模型切换默认场景
export const LLM_DEFAULT_INTENT = defaultToolList[0]

const Toolbar = ({ active, isMobile, onClick }: { active?: string; isMobile?: boolean; onClick: (data: any) => void }) => {
  const mySDK = useRef<AiPopup>()
  const { isFold, embedSource, appData } = useChatWithHistoryContext()
  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断
  const aiToolbars = chatPageConfigData?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))
  let toolList = (aiToolbars && aiToolbars.length) ? aiToolbars : []
  // 移动端去掉AI写新闻
  if (isMobile) {
    toolList = toolList.filter((item: ToolbarProps) => {
      return item?.type !== 'popup'
    })
  }

  LLM_DEFAULT_INTENT.prompt = toolList[0]?.prompt
  const btnBgGradientFrom = chatPageConfigData?.btnBgGradientFrom || '#5099FF'
  const btnBgGradientTo = chatPageConfigData?.btnBgGradientTo || '#7D67FF'

  const activeIntent = active?.includes('PPT') ? INTENT_PPT : active
  const toolRef = useRef<any>()
  const [open, setOpen] = useState(false)
  // 新增状态来存储可见和隐藏的工具列表项
  const [visibleTools, setVisibleTools] = useState<string[]>([])
  const [hiddenTools, setHiddenTools] = useState<string[]>([])

  const handleItemClick = (item: ToolbarProps) => {
    if (item?.label?.includes(INTENT_INCLUDES_SCHEDULE))
      localStorage.setItem('scheduleOrMeeting', 'schedule')
    else if (item?.label?.includes(INTENT_INCLUDES_MEETING))
      localStorage.setItem('scheduleOrMeeting', 'meeting')
    else localStorage.removeItem('scheduleOrMeeting')

    if (!item)
      return
    if (item.type === 'link')
      return window.open(item.link, '_blank')
    if (item?.type === 'popup') {
      mySDK.current?.switchPopups()
      return
    }
    onClick(item)
  }
  const showIcon = (icon: any, act: boolean) => {
    if (!icon)
      return null
    if (typeof icon === 'string' && icon.startsWith('http'))
      return <img className={SvgClass} src={icon} style={{ filter: act ? 'brightness(0) invert(1)' : '' }} />
    else
      return icon
  }

  const renderIntentBtns = (list: any[], className?: string) => (
    list.map(item => (
      <li
        id={item?.id}
        key={item?.label}
        data-label={item.label}
        className={
          className
            ? `${className} ${activeIntent === item?.label ? 'rounded-[2px] text-[#ffffff]' : ''}`
            : `btn-intent flex h-[32px] shrink-0 cursor-pointer items-center rounded-[16px] border border-[#DFE4E8] bg-[#fff] px-[12px] text-[14px] 
          ${item?.style} ${activeIntent === item?.label ? 'text-[#ffffff]' : ''}
          ${isMobile && '!h-[38px] rounded-[19px]'}`
        }
        onClick={() => handleItemClick(item)}
      >
        {showIcon(item?.icon, activeIntent === item?.label)}
        {item?.label}
      </li>
    ))
  )

  const handleContentWidthChange = () => {
    const contentWidth = toolRef.current?.offsetWidth
    const intentBtns = document.querySelectorAll('.btn-intent')
    const btnTotal = document.querySelector('.btn-total') as HTMLElement

    // 重置按钮样式
    Array.from(intentBtns).forEach((item: any) => {
      item.style.display = 'flex'
      item.style.background = `${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''}`
    })
    if (isMobile) {
      btnTotal.style.display = 'none'
      return
    }
    btnTotal.style.display = 'flex'

    const totalBtnsWidth = Array.from(intentBtns).reduce((total, el: any) => total + el.offsetWidth + 12, 0)

    if (totalBtnsWidth < contentWidth) {
      btnTotal.style.display = 'none'
      return
    }

    let totalWidth = btnTotal?.offsetWidth || 0
    let visible: string[] = []
    let hidden: string[] = []
    Array.from(intentBtns).forEach((item: any) => {
      totalWidth += (item.offsetWidth + 12)
      if (totalWidth < contentWidth) {
        item.style = `display:flex;background: ${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''} `
        visible.push(item.dataset.label)
      }
      else {
        item.style = 'display:none'
        hidden.push(item.dataset.label)
      }
    })

    // 当前项在更多里
    if (activeIntent && hidden.includes(activeIntent)) {
      // 重置按钮样式
      Array.from(intentBtns).forEach((item: any) => {
        item.style.display = 'flex'
        item.style.background = `${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''}`
      })
      btnTotal.style.display = 'flex'
      const activeBtn: any = Array.from(intentBtns).find((item: any) => item.dataset.label.includes(activeIntent))

      totalWidth = btnTotal?.offsetWidth + activeBtn.offsetWidth
      visible = [activeBtn.dataset.label]
      hidden = []
      Array.from(intentBtns).forEach((item: any) => {
        totalWidth += (item.offsetWidth + 12)
        if (totalWidth < contentWidth || visible.includes(item.dataset.label)) {
          item.style = `display:flex;background: ${activeIntent === item.dataset.label ? `linear-gradient(to right, ${btnBgGradientFrom}, ${btnBgGradientTo})` : ''} `
          visible.push(item.dataset.label)
        }
        else {
          item.style = 'display:none'
          hidden.push(item.dataset.label)
        }
      })
    }

    setVisibleTools(visible)
    setHiddenTools(hidden)
  }

  useEffect(() => {
    handleContentWidthChange()

    if (!isMobile) {
      const observer = new ResizeObserver(handleContentWidthChange)
      observer.observe(toolRef.current)
      return () => observer.disconnect()
    }
  }, [isMobile, activeIntent])

  useEffect(() => {
    embedSource && handleContentWidthChange()
  }, [embedSource])

  useEffect(() => {
    handleContentWidthChange()
  }, [isFold])

  useEffect(() => {
    if (!mySDK.current) {
      mySDK.current = new MySDK()
      mySDK.current.createButton({
        userCode: new Date().getTime().toString(),
      })
    }

    return () => {
      mySDK.current?.removePopup()
    }
  }, [])

  return (
    <ul
      ref={toolRef}
      className={`mb-[10px] flex w-full gap-x-[12px] ${isMobile && 'mb-[20px] gap-x-[8px] overflow-auto'}`}
      style={{ scrollbarWidth: 'none' }}
    >
      {renderIntentBtns(toolList)}
      <PortalToFollowElem
        open={open}
        onOpenChange={setOpen}
        placement='bottom-end'
        offset={4}
      >
        <PortalToFollowElemTrigger
          onClick={() => setOpen(v => !v)}
        >
          <li
            className={'btn-total hidden h-[32px] shrink-0 cursor-pointer items-center whitespace-nowrap rounded-[16px] border border-[#DFE4E8] bg-[#fff] px-[12px] text-[14px]'}
          >
            <IconAll className={SvgClass} />
            更多
          </li>
        </PortalToFollowElemTrigger>
        <PortalToFollowElemContent
          className="z-50"
        >
          <div
            className={'border--gray-200 min-w-[120px] rounded-lg border bg-white p-[16px] shadow-lg'}
            onMouseEnter={() => setOpen(true)}
            onMouseLeave={() => setOpen(false)}
            onClick={(e) => {
              setOpen(false)
              e.stopPropagation()
            }}
          >
            {renderIntentBtns(toolList.filter((item: any) => hiddenTools.includes(item.label)), 'flex items-center mb-[17px] cursor-pointer text-[#242933] last:mb-[0px] text-[14px]')
            }
          </div>
        </PortalToFollowElemContent>
      </PortalToFollowElem>
    </ul>
  )
}

export default Toolbar
