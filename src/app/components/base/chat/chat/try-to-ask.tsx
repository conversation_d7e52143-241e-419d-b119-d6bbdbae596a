import type { FC } from 'react'
import { memo } from 'react'
import { useTranslation } from 'react-i18next'
import type { OnSend } from '../types'
import Button from '@/app/components/base/button'
import Divider from '@/app/components/base/divider'
import cn from '@/utils/classnames'
import type { EmbedSource } from '@/models/share'

const LarkLeftIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <g fill="#6550CE" fill-rule="nonzero" opacity=".5">
      <path d="M5.31485975,3.23575343 L5.79250053,4.56304549 C6.32371828,6.03615532 7.48359419,7.19599314 8.95664688,7.72719185 L10.2839389,8.20483262 C10.4037224,8.24798371 10.4037224,8.41761685 10.2839389,8.46077555 L8.95664688,8.93841633 C7.48353705,9.46963408 6.32369924,10.62951 5.79250053,12.1025627 L5.31485975,13.4298547 C5.27170866,13.5496382 5.10207553,13.5496382 5.05891682,13.4298547 L4.58127605,12.1025627 C4.05005829,10.6294529 2.89018238,9.46961504 1.41712969,8.93841633 L0.0898376249,8.46077555 C-0.029945875,8.41762446 -0.029945875,8.24799133 0.0898376249,8.20483262 L1.41712969,7.72719185 C2.89023952,7.19597409 4.05007733,6.03609818 4.58127605,4.56304549 L5.05891682,3.23575343 C5.10206791,3.11522712 5.27170105,3.11522712 5.31485975,3.23575343 Z" transform="translate(3.3333 1.6667)" />
      <path d="M11.2898453,0.046313709 L11.5323797,0.718130565 C11.8017121,1.46361815 12.3887162,2.05136505 13.1349466,2.32069746 L13.8067635,2.56323186 C13.8677704,2.58555211 13.8677704,2.67111148 13.8067635,2.69268702 L13.1349466,2.93522142 C12.389459,3.20455383 11.8017121,3.79155793 11.5323797,4.53778832 L11.2898453,5.20960517 C11.2675251,5.27061208 11.1819657,5.27061208 11.1603902,5.20960517 L10.9178558,4.53778832 C10.6485233,3.79230073 10.0615192,3.20455383 9.31528886,2.93522142 L8.64347201,2.69268702 C8.5824651,2.67036677 8.5824651,2.5848074 8.64347201,2.56323186 L9.31528886,2.32069746 C10.0607764,2.05136505 10.6485233,1.46436095 10.9178558,0.718130565 L11.1603902,0.046313709 C11.1819657,-0.015437903 11.2682698,-0.015437903 11.2898453,0.046313709 L11.2898453,0.046313709 Z" transform="translate(3.3333 1.6667)" />
      <path d="M11.2898453,11.45762 L11.5323797,12.1294369 C11.8017121,12.8749245 12.3887162,13.4626714 13.1349466,13.7320038 L13.8067635,13.9745382 C13.8677704,13.9968584 13.8677704,14.0824178 13.8067635,14.1039933 L13.1349466,14.3465277 C12.389459,14.6158601 11.8017121,15.2028642 11.5323797,15.9490946 L11.2898453,16.6209115 C11.2675251,16.6819184 11.1819657,16.6819184 11.1603902,16.6209115 L10.9178558,15.9490946 C10.6485233,15.203607 10.0615192,14.6158601 9.31528886,14.3465277 L8.64347201,14.1039933 C8.5824651,14.0816731 8.5824651,13.9961137 8.64347201,13.9745382 L9.31528886,13.7320038 C10.0607764,13.4626714 10.6485233,12.8756673 10.9178558,12.1294369 L11.1603902,11.45762 C11.1819657,11.3966131 11.2682698,11.3966131 11.2898453,11.45762 Z" transform="translate(3.3333 1.6667)" />
    </g>
  </svg>
)

const LarkRightIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <g fill="#6550CE" fill-rule="nonzero" opacity=".5">
      <path d="M5.31485975,3.23575343 L5.79250053,4.56304549 C6.32371828,6.03615532 7.48359419,7.19599314 8.95664688,7.72719185 L10.2839389,8.20483262 C10.4037224,8.24798371 10.4037224,8.41761685 10.2839389,8.46077555 L8.95664688,8.93841633 C7.48353705,9.46963408 6.32369924,10.62951 5.79250053,12.1025627 L5.31485975,13.4298547 C5.27170866,13.5496382 5.10207553,13.5496382 5.05891682,13.4298547 L4.58127605,12.1025627 C4.05005829,10.6294529 2.89018238,9.46961504 1.41712969,8.93841633 L0.0898376249,8.46077555 C-0.029945875,8.41762446 -0.029945875,8.24799133 0.0898376249,8.20483262 L1.41712969,7.72719185 C2.89023952,7.19597409 4.05007733,6.03609818 4.58127605,4.56304549 L5.05891682,3.23575343 C5.10206791,3.11522712 5.27170105,3.11522712 5.31485975,3.23575343 Z" transform="matrix(-1 0 0 1 16.6667 1.6667)" />
      <path d="M11.2898453,0.046313709 L11.5323797,0.718130565 C11.8017121,1.46361815 12.3887162,2.05136505 13.1349466,2.32069746 L13.8067635,2.56323186 C13.8677704,2.58555211 13.8677704,2.67111148 13.8067635,2.69268702 L13.1349466,2.93522142 C12.389459,3.20455383 11.8017121,3.79155793 11.5323797,4.53778832 L11.2898453,5.20960517 C11.2675251,5.27061208 11.1819657,5.27061208 11.1603902,5.20960517 L10.9178558,4.53778832 C10.6485233,3.79230073 10.0615192,3.20455383 9.31528886,2.93522142 L8.64347201,2.69268702 C8.5824651,2.67036677 8.5824651,2.5848074 8.64347201,2.56323186 L9.31528886,2.32069746 C10.0607764,2.05136505 10.6485233,1.46436095 10.9178558,0.718130565 L11.1603902,0.046313709 C11.1819657,-0.015437903 11.2682698,-0.015437903 11.2898453,0.046313709 L11.2898453,0.046313709 Z" transform="matrix(-1 0 0 1 16.6667 1.6667)" />
      <path d="M11.2898453,11.45762 L11.5323797,12.1294369 C11.8017121,12.8749245 12.3887162,13.4626714 13.1349466,13.7320038 L13.8067635,13.9745382 C13.8677704,13.9968584 13.8677704,14.0824178 13.8067635,14.1039933 L13.1349466,14.3465277 C12.389459,14.6158601 11.8017121,15.2028642 11.5323797,15.9490946 L11.2898453,16.6209115 C11.2675251,16.6819184 11.1819657,16.6819184 11.1603902,16.6209115 L10.9178558,15.9490946 C10.6485233,15.203607 10.0615192,14.6158601 9.31528886,14.3465277 L8.64347201,14.1039933 C8.5824651,14.0816731 8.5824651,13.9961137 8.64347201,13.9745382 L9.31528886,13.7320038 C10.0607764,13.4626714 10.6485233,12.8756673 10.9178558,12.1294369 L11.1603902,11.45762 C11.1819657,11.3966131 11.2682698,11.3966131 11.2898453,11.45762 Z" transform="matrix(-1 0 0 1 16.6667 1.6667)" />
    </g>
  </svg>

)

type TryToAskProps = {
  isMobile?: boolean
  embedSource?: EmbedSource
  suggestedQuestions: string[]
  onSend: OnSend
}
const TryToAsk: FC<TryToAskProps> = ({
  isMobile,
  embedSource,
  suggestedQuestions,
  onSend,
}) => {
  const { t } = useTranslation()
  const isEmbedMobile = embedSource && isMobile

  return (
    <div className={embedSource ? 'mb-[80px]' : 'mb-2 py-2'}>
      <div className={cn(`mb-2.5 flex items-center justify-between gap-2 ${embedSource && !isMobile && 'px-14'}`, isMobile && 'justify-end')}>
        {embedSource ? (
          <div
            className='h-[1px] grow'
            style={{
              background: 'linear-gradient(to left, #ACA1E3, #FAFAFD)',
            }}
          />
        ) : (
          <Divider bgStyle='gradient' className='h-px grow rotate-180' />
        )}
        <div className='system-xs-medium-uppercase flex shrink-0 items-center text-text-tertiary'>
          {embedSource && <LarkLeftIcon className="h-[20px] w-[20px]" />}
          <span className={`${embedSource && '!text-[#6550CE]'}`}>{embedSource ? '可以试着这样问噢' : t('appDebug.feature.suggestedQuestionsAfterAnswer.tryToAsk')}</span>
          {embedSource && <LarkRightIcon className="h-[20px] w-[20px]" />}
        </div>
        {embedSource ? (
          <div
            className='h-[1px] grow'
            style={{
              background: 'linear-gradient(to right, #ACA1E3, #FAFAFD)',
            }}
          />
        ) : (
          <Divider bgStyle='gradient' className='h-px grow' />
        )}
      </div>
      <div className={cn('flex flex-wrap justify-center', isMobile && 'justify-end', embedSource && isMobile && 'block')}>
        {
          suggestedQuestions.map((suggestQuestion, index) => (
            <Button
              size={embedSource ? 'medium' : 'small'}
              key={index}
              variant='secondary-accent'
              className={`mb-1 mr-1 last:mr-0 ${!isEmbedMobile && 'last:mr-0'} ${embedSource && 'rounded-[16px] text-center text-[12px] text-[#434B5B]'} ${isEmbedMobile && 'block w-fit justify-center'}`}
              onClick={() => onSend(suggestQuestion)}
            >
              {suggestQuestion}
            </Button>
          ))
        }
      </div>
    </div>
  )
}

export default memo(TryToAsk)
