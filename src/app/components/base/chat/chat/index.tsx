import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import { cloneDeep, debounce } from 'lodash-es'
import { useShallow } from 'zustand/react/shallow'
import type {
  ChatConfig,
  ChatItem,
  Feedback,
  OnRegenerate,
  OnSend,
} from '../types'
import type { ThemeBuilder } from '../embedded-chatbot/theme/theme-context'
import Question from './question'
import Answer from './answer'
import ChatInputArea from './chat-input-area'
import TryToAsk from './try-to-ask'
import { ChatContextProvider } from './context'
import type { InputForm } from './type'
import Toolbar, { LLM_DEFAULT_INTENT } from './toolbar'
import { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE, SHOW_SUGGESTION_INTENT } from './u-const'
import ModelSwitching from './model-switching'
import styles from './index.module.css'
import cn from '@/utils/classnames'
import type { Emoji } from '@/app/components/tools/types'
import Button from '@/app/components/base/button'
import { StopCircle } from '@/app/components/base/icons/src/vender/solid/mediaAndDevices'
import AgentLogModal from '@/app/components/base/agent-log-modal'
import PromptLogModal from '@/app/components/base/prompt-log-modal'
import { useStore as useAppStore } from '@/app/components/app/store'
import type { AppData, EmbedSource } from '@/models/share'
import LarkRobot from '@/assets/lark-app-robot.gif'
import IconSuggest from '@/assets/lark-suggest.svg'
import IconSuggestTitle from '@/assets/lark-suggest-title.svg'
import { fetchAiPptConfigApi, fetchAiPptCreatTaskApi, fetchAiPptResourcesSaveApi } from '@/service/share'
import McpTools from './mcp-tools'
import Image from '@/app/components/base/image'

export type ChatProps = {
  appData?: AppData
  chatList: ChatItem[]
  config?: ChatConfig
  isResponding?: boolean
  noStopResponding?: boolean
  onStopResponding?: () => void
  noChatInput?: boolean
  onSend?: OnSend
  inputs?: Record<string, any>
  inputsForm?: InputForm[]
  onRegenerate?: OnRegenerate
  chatContainerClassName?: string
  chatContainerInnerClassName?: string
  chatFooterClassName?: string
  chatFooterInnerClassName?: string
  suggestedQuestions?: string[]
  showPromptLog?: boolean
  questionIcon?: ReactNode
  answerIcon?: ReactNode
  allToolIcons?: Record<string, string | Emoji>
  onAnnotationEdited?: (question: string, answer: string, index: number) => void
  onAnnotationAdded?: (annotationId: string, authorName: string, question: string, answer: string, index: number) => void
  onAnnotationRemoved?: (index: number) => void
  chatNode?: ReactNode
  onFeedback?: (messageId: string, feedback: Feedback) => void
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  hideLogModal?: boolean
  themeBuilder?: ThemeBuilder
  switchSibling?: (siblingMessageId: string) => void
  showFeatureBar?: boolean
  showFileUpload?: boolean
  onFeatureBarClick?: (state: boolean) => void
  noSpacing?: boolean
  inputDisabled?: boolean
  isMobile?: boolean
  sidebarCollapseState?: boolean
  embedSource?: EmbedSource
  larkInfo?: any
  conversationId?: string
  currentIntent?: { current: string }
  updateIntent?: (intent: string, prompt?: string) => void
  updateLlm?: (name: string, key: string) => void
  updateIntentSilent: (intent: string) => void
  updateLastChatIntent: (intent: string) => void
}

const IconRefresh = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <path d="M8.38191281,13.1111111 L4.33731165,13.1111111 L4.33731165,17 M11.6175937,6.88888889 L15.6621949,6.88888889 L15.6621949,3 M4,7.66931111 C4.4535454,6.58995778 5.21291927,5.65458667 6.19180173,4.96951222 C7.17068419,4.28443 8.33083759,3.87696 9.53850696,3.79373 C10.746144,3.7105 11.953053,3.95464444 13.023578,4.49847444 C14.094103,5.04230444 14.9836726,5.86419778 15.5927087,6.87031556 M16,12.3310778 C15.5464384,13.4104 14.7870241,14.3457556 13.8081497,15.0308222 C12.8292754,15.7159667 11.6703353,16.1229 10.4626983,16.2061222 C9.25506131,16.2893444 8.04706837,16.0452778 6.97657569,15.5013778 C5.90607492,14.9575556 5.01585012,14.1358333 4.40683025,13.1297" stroke="#3B67FF" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd" />
  </svg>
)

const IconClose = ({ className }: { className: string }) => (
  <svg className={className} viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" p-id="5061" width="24" height="24"><path d="M572.267 512l225.867-225.867c16.533-16.533 16.533-43.733 0-60.267s-43.733-16.533-60.267 0L512 451.733 286.133 225.866c-16.533-16.533-43.733-16.533-60.267 0s-16.533 43.733 0 60.267L451.733 512 225.866 737.867c-16.533 16.533-16.533 43.733 0 60.267 8.267 8.267 19.2 12.533 30.133 12.533s21.867-4.267 30.133-12.533l225.867-225.867 225.867 225.867c8.267 8.267 19.2 12.533 30.133 12.533s21.867-4.267 30.133-12.533c16.533-16.533 16.533-43.733 0-60.267L572.265 512z" fill="#707070" p-id="5062"></path></svg>
)
const IconArrowDown = ({ className }: { className: string }) => (
  <svg className={className} viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M758.976 305.28L534.208 531.712a32 32 0 0 1-45.376 0L264.576 306.624a28.992 28.992 0 0 1 0-41.152 28.992 28.992 0 0 1 41.152 0l205.824 205.824 206.272-206.72a28.992 28.992 0 0 1 41.152 0 29.248 29.248 0 0 1 0 40.704z m-41.152 207.616a28.992 28.992 0 0 1 41.152 0c0 0.448 0.448 0.448 0.448 0.896a28.992 28.992 0 0 1 0 41.152l-225.28 224.832a32 32 0 0 1-45.184 0L264.576 554.88a28.992 28.992 0 0 1 0-41.152 28.992 28.992 0 0 1 41.152 0l205.824 205.824 206.272-206.72z" fill="#037DF3" p-id="6599"></path></svg>
)

const Chat: FC<ChatProps> = ({
  appData,
  config,
  onSend,
  inputs,
  inputsForm,
  onRegenerate,
  chatList,
  isResponding,
  noStopResponding,
  onStopResponding,
  noChatInput,
  chatContainerClassName,
  chatContainerInnerClassName,
  chatFooterClassName,
  chatFooterInnerClassName,
  suggestedQuestions,
  showPromptLog,
  questionIcon,
  answerIcon,
  onAnnotationAdded,
  onAnnotationEdited,
  onAnnotationRemoved,
  chatNode,
  onFeedback,
  chatAnswerContainerInner,
  hideProcessDetail,
  hideLogModal,
  themeBuilder,
  switchSibling,
  showFeatureBar,
  showFileUpload,
  onFeatureBarClick,
  noSpacing,
  inputDisabled,
  isMobile,
  sidebarCollapseState,
  embedSource,
  larkInfo,
  conversationId,
  currentIntent,
  updateIntent,
  updateLlm,
  updateIntentSilent,
  updateLastChatIntent,
}) => {
  // 提问示例条数
  const SUGGEST_MAX = isMobile ? 3 : 4
  const { t } = useTranslation()
  const { currentLogItem, setCurrentLogItem, showPromptLogModal, setShowPromptLogModal, showAgentLogModal, setShowAgentLogModal } = useAppStore(useShallow(state => ({
    currentLogItem: state.currentLogItem,
    setCurrentLogItem: state.setCurrentLogItem,
    showPromptLogModal: state.showPromptLogModal,
    setShowPromptLogModal: state.setShowPromptLogModal,
    showAgentLogModal: state.showAgentLogModal,
    setShowAgentLogModal: state.setShowAgentLogModal,
  })))
  const [width, setWidth] = useState(0)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const chatContainerInnerRef = useRef<HTMLDivElement>(null)
  const chatFooterRef = useRef<HTMLDivElement>(null)
  const chatFooterInnerRef = useRef<HTMLDivElement>(null)
  const userScrolledRef = useRef(false)
  const [selectLlm, setSelectLlm] = useState<any>()
  const [refreshCount, setRefreshCount] = useState<number>(0)
  const [randomSuggest, setRandomSuggest] = useState<any[]>([])
  const [pptCloseBtnShow, setPptCloseBtnShow] = useState<boolean>(false)
  const [pptCloseBtnCanClick, setPptCloseBtnCanClick] = useState<boolean>(true)
  const [pptLoadingSate, setPptLoadingSate] = useState<boolean>(false)
  const isEmbedMobile = embedSource && isMobile
  const extensionData = useMemo(() => appData?.site?.extension_data ? JSON.parse(appData.site.extension_data) : {}, [appData])
  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断
  const aiToolbars = extensionData?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))

  // 新增状态，用于控制回到底部图标的显示和隐藏
  const [showScrollToBottomIcon, setShowScrollToBottomIcon] = useState(false)

  // 处理滚动事件，当向上滚动时显示图标
  const handleScroll = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, clientHeight, scrollHeight } = chatContainerRef.current
      const distanceToBottom = scrollHeight - (scrollTop + clientHeight)
      const shouldShow = distanceToBottom > 20 && userScrolledRef.current
      setShowScrollToBottomIcon(prev => prev !== shouldShow ? shouldShow : prev)
    }
  }, [])

  // 处理点击图标事件，回到最底部并隐藏图标
  const handleScrollToBottomAndHideIcon = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollHeight } = chatContainerRef.current
      // 仅当不在底部时执行滚动
      chatContainerRef.current.scrollTo({
        top: scrollHeight,
        behavior: 'smooth',
      })
      setShowScrollToBottomIcon(false)
      userScrolledRef.current = false
    }
  }, [])

  const handleScrollToBottom = useCallback(() => {
    if (chatList.length > 1 && chatContainerRef.current && !userScrolledRef.current)
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight
  }, [chatList.length])

  const handleWindowResize = useCallback(() => {
    if (chatContainerRef.current)
      setWidth(document.body.clientWidth - (chatContainerRef.current?.clientWidth + 16) - 8)

    if (chatContainerRef.current && chatFooterRef.current)
      chatFooterRef.current.style.width = `${chatContainerRef.current.clientWidth}px`

    if (chatContainerInnerRef.current && chatFooterInnerRef.current)
      chatFooterInnerRef.current.style.width = `${chatContainerInnerRef.current.clientWidth}px`
  }, [])

  const handleNewTopic = () => {
    updateIntent?.('')
  }
  // 一键生成PPT
  const handleOneKeyGenerationPpt = async (content: string, title: string, curChatId: string) => {
    // onSend?.('确认大纲内容，直接生成', [])
    if (pptLoadingSate)
      return false
    setPptLoadingSate(true)
    const configRes: any = await fetchAiPptConfigApi()
    const { api_key, channel, code } = configRes || { }
    const res: any = await fetchAiPptCreatTaskApi({
      type: 7,
      title,
      content,
    })
    const { id: taskId } = res?.data || {}
    try {
      await AipptIframe.show({
        appkey: api_key,
        channel,
        code,
        editorModel: true,
        options: {
          custom_generate: {
            taskId,
            step: 2,
          },
        },
        async onMessage(eventType: any, data: any) {
          // console.log(eventType, data, 'onMessage----add')
          setPptCloseBtnCanClick(true)
          if (eventType === 'START')
            setPptCloseBtnCanClick(false)
          if (['PPT_SAVE', 'GENERATE_PPT_SUCCESS'].includes(eventType)) {
            setPptCloseBtnCanClick(true)
            chatList.map((item) => {
              if (item.id === curChatId)
                Object.assign(item, { ppt: data })
              return item
            })
            await fetchAiPptResourcesSaveApi(conversationId || '', { ...data, messageId: curChatId })
          }
        },
      })
      setPptCloseBtnShow(true)
    }
    catch (e) {
      // console.log(e, 'catch exception')
    }
  }
  const handleCloseAiPptFrame = () => {
    if (pptCloseBtnCanClick) {
      AipptIframe.deleteIframe()
      setPptCloseBtnShow(false)
      document.getElementById('aippt-iframe-modal')?.remove()
    }
    setPptLoadingSate(false)
  }
  const hanldePptEdit = async (ppt: any, curChatId: string) => {
    const { id } = ppt
    const configRes: any = await fetchAiPptConfigApi()
    const { api_key, channel, code } = configRes || {}
    try {
      await AipptIframe.show({
        appkey: api_key,
        channel,
        code,
        editorModel: true,
        routerOptions: {
          list: ['workspace', 'generate', 'editor'],
          // 存在editor 并且存在id的情况下 则直接跳转至编辑器页面
          editor: {
            showLogo: 3,
            id,
          },
        },
        async onMessage(eventType: any, data: any) {
          // console.log(eventType, data, 'onMessage----edit')
          setPptCloseBtnCanClick(true)
          if (eventType === 'START')
            setPptCloseBtnCanClick(false)
          if (['PPT_SAVE', 'GENERATE_PPT_SUCCESS'].includes(eventType)) {
            setPptCloseBtnCanClick(true)
            chatList.map((item) => {
              if (item.id === curChatId)
                item.ppt = data
              return item
            })
            await fetchAiPptResourcesSaveApi(conversationId || '', { ...data, messageId: curChatId })
          }
        },
      })
    }
    catch (e) {
      // console.log(e, 'catch exception')
    }
    setPptCloseBtnShow(true)
  }

  const handleChangeIntent = ({ label, prompt }: { label: string; prompt: string }) => {
    updateIntent?.(label, prompt)
    handleScrollToBottomAndHideIcon()
  }

  const handleChangeModel = (data: any) => {
    // TODO: 临时方案，后续需要根据需求调整

    // if (currentIntent.current === LLM_DEFAULT_INTENT.label)
    //   updateLlm(data.label, data.value)
    // else if (data && selectLlm) {
    //   updateIntent(LLM_DEFAULT_INTENT.label, LLM_DEFAULT_INTENT.prompt)
    // }

    // 切换模型
    updateLlm?.(data.label, data.value)
    setSelectLlm(data)
    handleScrollToBottomAndHideIcon()
  }

  const handleRefreshSuggest = () => {
    const temp = []
    const suggests = cloneDeep(config?.suggested_questions) || []

    for (let i = 0; i < SUGGEST_MAX; i++) {
      const randomIndex = Math.floor(Math.random() * suggests?.length)
      temp.push(suggests[randomIndex])
      suggests.splice(randomIndex, 1) // 从副本中移除已选中的元素
    }

    setRandomSuggest(temp)
  }

  const handleCreateSchedule = (data: any) => {
    const scheduleOrMeeting = localStorage.getItem('scheduleOrMeeting')
    const typeTit = scheduleOrMeeting === 'schedule' ? INTENT_INCLUDES_SCHEDULE : scheduleOrMeeting === 'meeting' ? INTENT_INCLUDES_MEETING : ''
    onSend?.(`创建${typeTit}`, [], false, null, data)
  }
  const handleGenerationMeeting = (data: any) => {
    onSend?.('同步预定腾讯会议', [], false, null, data)
  }

  // 意图识别选择意图
  const handleSelectIntent = ({ intent, question }: { intent: string; question: string }) => {
    // updateIntentSilent?.(intent)
    const questionList = chatList.filter(item => !item.isAnswer)
    const lastQuestion = questionList[questionList.length - 1]?.content
    if (lastQuestion === question) {
      updateLastChatIntent?.(intent)
      onSend?.(question, [], false, null, { isIntentRecognition: true })
    }
    else {
      return false
    }
  }

  useEffect(() => {
    handleScrollToBottom()
    handleWindowResize()
  }, [handleScrollToBottom, handleWindowResize])

  useEffect(() => {
    if (chatContainerRef.current) {
      requestAnimationFrame(() => {
        handleScrollToBottom()
        handleWindowResize()
      })
    }
  })

  // 处理通过历史会话进入更新意图
  useEffect(() => {
    if (chatList?.length > 0) {
      const chatLastObj = chatList[chatList.length - 1]
      // chatLastObj?.intent && updateLastChatIntent?.(chatLastObj.intent)
      chatLastObj?.intent && updateIntent?.(chatLastObj.intent)
    }
  }, [conversationId])

  useEffect(() => {
    window.addEventListener('resize', debounce(handleWindowResize))
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [handleWindowResize])

  useEffect(() => {
    if (chatFooterRef.current && chatContainerRef.current) {
      // container padding bottom
      const resizeContainerObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { blockSize } = entry.borderBoxSize[0]
          chatContainerRef.current!.style.paddingBottom = `${blockSize + 20}px`
          handleScrollToBottom()
        }
      })
      resizeContainerObserver.observe(chatFooterRef.current)

      // footer width
      const resizeFooterObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { inlineSize } = entry.borderBoxSize[0]
          chatFooterRef.current!.style.width = `${inlineSize}px`
        }
      })
      resizeFooterObserver.observe(chatContainerRef.current)

      return () => {
        resizeContainerObserver.disconnect()
        resizeFooterObserver.disconnect()
      }
    }
  }, [handleScrollToBottom])

  useEffect(() => {
    const chatContainer = chatContainerRef.current
    if (chatContainer) {
      const setUserScrolled = () => {
        // eslint-disable-next-line sonarjs/no-gratuitous-expressions
        if (chatContainer) // its in event callback, chatContainer may be null
          userScrolledRef.current = chatContainer.scrollHeight - chatContainer.scrollTop >= chatContainer.clientHeight + 100
      }
      chatContainer.addEventListener('scroll', setUserScrolled)
      return () => chatContainer.removeEventListener('scroll', setUserScrolled)
    }
  }, [])

  useEffect(() => {
    if (!sidebarCollapseState)
      setTimeout(() => handleWindowResize(), 200)
  }, [handleWindowResize, sidebarCollapseState])

  useEffect(() => {
    handleRefreshSuggest()
  }, [config?.suggested_questions])

  // 发送消息时滚至底部
  useEffect(() => {
    userScrolledRef.current = false
    handleScrollToBottom()
  }, [chatList.length])

  useEffect(() => {
    if (!chatList.length && embedSource && aiToolbars?.length)
      updateIntent?.(aiToolbars[0].label, aiToolbars[0].prompt)
  }, [chatList, embedSource, aiToolbars])

  useEffect(() => {
    const container = chatContainerRef.current
    const debouncedScroll = debounce(handleScroll, 300)
    if (container) {
      container.addEventListener('scroll', debouncedScroll)
      // 主动触发一次初始化
      handleScroll()
    }
    return () => {
      container?.removeEventListener('scroll', debouncedScroll)
      debouncedScroll.cancel()
    }
  }, [handleScroll])

  const hasTryToAsk = config?.suggested_questions_after_answer?.enabled && !!suggestedQuestions?.length && onSend

  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || LarkRobot // AI机器人动画
  const aiTopBgGradientFrom = chatPageConfigData?.aiTopBgGradientFrom || '#d7dafc'// AI顶部背景渐变色起始
  const aiTopBgGradientTo = chatPageConfigData?.aiTopBgGradientTo || '#fafafd'// AI顶部背景渐变色结束
  const aiBtmGradientFrom = chatPageConfigData?.aiBtmGradientFrom || '#d4ddfa' // AI底部背景渐变色起始
  const aiBtmBgGradientTo = chatPageConfigData?.aiBtmBgGradientTo || '#fafafd' // AI底部背景渐变色结束

  return (
    <ChatContextProvider
      config={config}
      chatList={chatList}
      isResponding={isResponding}
      showPromptLog={showPromptLog}
      questionIcon={questionIcon}
      answerIcon={answerIcon}
      onSend={onSend}
      onRegenerate={onRegenerate}
      onAnnotationAdded={onAnnotationAdded}
      onAnnotationEdited={onAnnotationEdited}
      onAnnotationRemoved={onAnnotationRemoved}
      onFeedback={onFeedback}
    >
      {pptCloseBtnShow && <div className='absolute left-[60px] top-[40px] z-20 flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-[50%] bg-white ' style={{ boxShadow: '0 0 16px 1px rgba(0,0,0,.12)' }} onClick={() => { handleCloseAiPptFrame() }}>
        <IconClose className="h-[24px] w-[24px]"/>
      </div>}
      <div className='relative h-full' style={{ backgroundImage: (embedSource && !isMobile) ? `linear-gradient(to bottom, ${aiTopBgGradientFrom}, ${aiTopBgGradientTo} 20%)` : '' }}>
        <div
          ref={chatContainerRef}
          className={cn('relative h-full overflow-y-auto overflow-x-hidden', chatContainerClassName)}
        >
          {chatNode}
          <div
            ref={chatContainerInnerRef}
            className={cn('w-full', !noSpacing && 'px-8', chatContainerInnerClassName, embedSource && 'mb-100px')}
          >
            {
              embedSource && (
                <div className="mb-[26px]">
                  {!isMobile && <h1
                    className='flex items-center justify-center text-center text-[24px] font-semibold text-[#242933]'
                    style={{ fontFamily: 'PingFangSC' }}
                  >
                    <Image width={80} height={80} src={aiRobotGifUrl} alt="" />
                      您好，欢迎使用<span className='lark-gradient-title  ml-[10px]'>{appData?.site.title}</span>
                  </h1>}
                  {config?.opening_statement && (
                    <>
                      <div
                        className={
                          `mb-[64px] ${isMobile && styles.mobilePromptBg} 
                            ${isMobile && '!mb-[0px] flex rounded-[12px] bg-[#fff] py-[16px] pr-[16px] text-[14px] !leading-[26px] !text-[#242933]'}
                            ${config?.suggested_questions && isEmbedMobile && 'pb-[22px]'}
                          `}
                      >
                        {isMobile && <Image src={aiRobotGifUrl} width={78} height={78} className='mx-auto h-[78px] w-[78px] shrink-0' alt="" />}
                        <p className={`mx-auto max-w-[640px] text-justify text-[12px] leading-[20px] text-[#5C6574] ${isMobile && 'text-[14px] leading-[26px] !text-[#242933]'}`}>
                          {config?.opening_statement}
                        </p>
                      </div>

                      {!chatList.length && config?.suggested_questions && (
                        <div className={`${isMobile && 'rounded-[12px] bg-white p-[16px]'} ${isEmbedMobile && '-mt-[14px]'}`}>
                          <div className="mb-[20px] flex justify-between text-[14px]">
                            <p className={`text-[#8C97A4] ${isMobile && 'flex items-center font-semibold !text-[#242933]'}`}>{isEmbedMobile && <Image src={IconSuggestTitle} className="mr-[8px] h-[20px] w-[20px]" alt="" />}您可以这样向我提问</p>
                            {config?.suggested_questions.length > SUGGEST_MAX && (
                              <p className='flex cursor-pointer items-center text-[#3B67FF]' onClick={handleRefreshSuggest} ><IconRefresh className={`mr-[6px] h-[20px] w-[20px] ${isMobile && '!h-[16px] !w-[16px]'}`} />换一批</p>
                            )}
                          </div>
                          <ul className={`flex min-h-[110px] flex-wrap justify-between gap-y-[16px] ${isMobile && 'flex-col'}`}>
                            {
                              randomSuggest?.map(question => (
                                <li
                                  className={`flex min-h-[48px] shrink-0 cursor-pointer rounded-[8px] bg-[#E2E2FF] px-[20px] py-[14px] text-[14px] text-[#242933] last:mr-0 ${isMobile && 'bg-[#f8f8f9] p-[14px]'}`}
                                  style={{ width: isMobile ? '100%' : 'calc(50% - 12px)' }}
                                  key={question}
                                  onClick={() => onSend?.(question)}
                                >
                                  <Image src={IconSuggest} className="mr-[8px] h-[20px] w-[20px]" alt="" />
                                  {question}
                                </li>
                              ))
                            }
                          </ul>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )
            }
            {
              chatList.filter(item => !item.isHide).map((item, index) => {
                if (item.isAnswer) {
                  const isLast = item.id === chatList[chatList.length - 1]?.id
                  return (
                    <Answer
                      appData={appData}
                      key={item.id}
                      item={item}
                      ppt={item?.ppt || {}}
                      question={chatList[index - 1]?.content}
                      index={index}
                      config={config}
                      answerIcon={answerIcon}
                      responding={isLast && isResponding}
                      showPromptLog={showPromptLog}
                      chatAnswerContainerInner={chatAnswerContainerInner}
                      hideProcessDetail={hideProcessDetail}
                      noChatInput={noChatInput}
                      switchSibling={switchSibling}
                      embedSource={embedSource}
                      isLast={isLast}
                      isMobile={isMobile}
                      onNewTopic={handleNewTopic}
                      onOneKeyGenerationPpt={() => { handleOneKeyGenerationPpt(item.content, chatList[index - 1]?.content, item.id) }}
                      onEditClick={() => { hanldePptEdit(item?.ppt, item.id) }}
                      onCreateSchedule={handleCreateSchedule}
                      onOneKeyGenerationMeeting={handleGenerationMeeting}
                      hideOperator={!SHOW_SUGGESTION_INTENT.includes(currentIntent?.current || '')}
                      updateIntent={updateIntent}
                      onSelectIntent={handleSelectIntent}
                      activeIntent={currentIntent?.current}
                      generationPptLoading={pptLoadingSate}
                    />
                  )
                }
                return (
                  <Question
                    key={item.id}
                    item={item}
                    questionIcon={questionIcon}
                    theme={themeBuilder?.theme}
                    switchSibling={switchSibling}
                    avatar={larkInfo?.avatar_url || larkInfo?.avatarUrl }
                    userName={larkInfo?.name || larkInfo?.userName}
                    embedSource={embedSource}
                  />
                )
              })
            }
            {
              hasTryToAsk && embedSource && SHOW_SUGGESTION_INTENT.includes(currentIntent?.current || '') && (
                <TryToAsk
                  embedSource={embedSource}
                  isMobile={isMobile}
                  suggestedQuestions={suggestedQuestions}
                  onSend={onSend}
                />
              )
            }
          </div>
        </div>
        <div
          className={`absolute bottom-0 flex justify-center bg-chat-input-mask ${(hasTryToAsk || !noChatInput || !noStopResponding) && chatFooterClassName} ${embedSource && !isMobile && 'px-[15.8vw]'}`}
          ref={chatFooterRef}
          style={{
            background: embedSource ? (isMobile ? 'linear-gradient(0deg, #EEF3FF 50%, #f5f6f8 100%)' : `linear-gradient(0deg, ${aiBtmGradientFrom}, ${aiBtmBgGradientTo} 60%)`) : 'linear-gradient(0deg, #F9FAFB 40%, rgba(255, 255, 255, 0.00) 100%)',
          }}
        >
          <div
            ref={chatFooterInnerRef}
            className={cn('relative', chatFooterInnerClassName)}
          >
            {
              !noStopResponding && isResponding && (
                <div className='mb-2 flex justify-center'>
                  <Button onClick={onStopResponding}>
                    <StopCircle className='mr-[5px] h-3.5 w-3.5 text-gray-500' />
                    <span className='text-xs font-normal text-gray-500'>{t('appDebug.operation.stopResponding')}</span>
                  </Button>
                </div>
              )
            }
            {
              hasTryToAsk && !embedSource && (
                <TryToAsk
                  embedSource={embedSource}
                  suggestedQuestions={suggestedQuestions}
                  onSend={onSend}
                  isMobile={isMobile}
                />
              )
            }
            {
              !noChatInput && (
                <div className={embedSource ? 'relative' : 'relative mx-auto w-full max-w-[720px]' } >
                  {embedSource && (<Toolbar active={currentIntent?.current} isMobile={isMobile} onClick={handleChangeIntent} />)}
                  <div className={`${isMobile && 'flex items-center gap-[10px]'} relative ${isMobile && embedSource && 'mb-10px'}`}>
                    {showScrollToBottomIcon && (
                      <div
                        className={'absolute bottom-[245px] right-[0px] z-[16] flex h-[40px] w-[40px] cursor-pointer items-center justify-center rounded-[50%] bg-white'}
                        style={{ boxShadow: '0 0 16px 1px rgba(0,0,0,.12)' }}
                        onClick={handleScrollToBottomAndHideIcon}
                      >
                        <IconArrowDown className="h-[24px] w-[24px]"/>
                      </div>)}
                    <ChatInputArea
                      disabled={inputDisabled}
                      showFeatureBar={showFeatureBar}
                      showFileUpload={showFileUpload}
                      featureBarDisabled={isResponding}
                      onFeatureBarClick={onFeatureBarClick}
                      visionConfig={config?.file_upload}
                      speechToTextConfig={config?.speech_to_text}
                      onSend={onSend}
                      inputs={inputs}
                      inputsForm={inputsForm}
                      theme={themeBuilder?.theme}
                      isResponding={isResponding}
                      embedSource={embedSource}
                      isMobile={isMobile}
                      intent={currentIntent?.current}
                      footer={
                        embedSource
                        ? <div className={(embedSource && !isMobile) ? 'absolute bottom-[10px] left-[4px] z-[11] flex h-[32px] gap-[10px]' : 'flex gap-[10px]'}>
                            <ModelSwitching models={extensionData.chatModels || []} showBuiltIn={Boolean(currentIntent?.current && (LLM_DEFAULT_INTENT.label !== currentIntent.current))} isMobile={isMobile} onClickActive={handleChangeModel} />
                            {LLM_DEFAULT_INTENT.label === currentIntent?.current && <McpTools isMobile={Boolean(isMobile)}/>}
                        </div>
                        : null
                      }
                    />
                  </div>
                  {appData?.site?.custom_disclaimer && <div className='mt-[16px] text-center text-xs text-gray-500'>
                    {appData.site.custom_disclaimer}
                  </div>}
                </div>
              )
            }
          </div>
        </div>
        {showPromptLogModal && !hideLogModal && (
          <PromptLogModal
            width={width}
            currentLogItem={currentLogItem}
            onCancel={() => {
              setCurrentLogItem()
              setShowPromptLogModal(false)
            }}
          />
        )}
        {showAgentLogModal && !hideLogModal && (
          <AgentLogModal
            width={width}
            currentLogItem={currentLogItem}
            onCancel={() => {
              setCurrentLogItem()
              setShowAgentLogModal(false)
            }}
          />
        )}
      </div>
    </ChatContextProvider>
  )
}

export default memo(Chat)
