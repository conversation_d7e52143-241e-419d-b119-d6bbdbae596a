import { useState } from 'react'
import Button from '../../../button'
import { useChatWithHistoryContext } from '../../chat-with-history/context'
import { INTENT_INCLUDES_MEETING, INTENT_INCLUDES_SCHEDULE } from '../u-const'

type Employee = {
  guid: string
  username: string
  fullname: string
  department: string
  keyword: string
}

type ScheduleCardProps = {
  data: {
    summary: string
    description: string
    start_time: string
    end_time: string
    event: string
    meeting_location?: string
    employees: { keyword: string; employees: Employee[] }[]
  }
  isLast?: boolean
  isEmbedMobile?: boolean
  onCancel?: () => void
  onConfirm?: (data: any) => void
}

const List = ({ data, isEmbedMobile, scheduleTitleColor, hideBtn, onSelected }: { data: any; isEmbedMobile?: boolean;scheduleTitleColor?: string; hideBtn: boolean; onSelected: (data: any) => void }) => {
  const [employee, setEmployee] = useState<Employee>()
  const handleConfirm = (item: Employee) => {
    if (employee)
      return

    setEmployee(item)
    onSelected(item)
  }

  return (
    <div className={`p-[24px] hover:bg-[#F8F8F8] ${isEmbedMobile && 'px-[13px] pt-[16px] pb-[0px] last:!pb-[16px]'}`}>
      <p className={'mb-[16px]'} style={{ color: scheduleTitleColor }}>为你找到以下可能是[{data.keyword}]的人员</p>
      {data.employees?.map((item: Employee, index: number) => (
        <div className='flex items-center mb-[20px] last:mb-[0px]' key={index}>
          {!isEmbedMobile && <p className='font-semibold shrink-0'>[{item.fullname}]</p>}
          <p className={`mx-[16px] flex-1 ${isEmbedMobile && 'ml-[0px]'}`}>{isEmbedMobile && <span className="font-semibold mr-[4px]">{`[${item.fullname}]`}</span>}{item.department}</p>
          {!hideBtn && (
            <Button
              className={
                `!border-[#6B4EFF] text-[#6B4EFF] rounded-[4px] px-[12px] h-[24px] text-[12px] 
                ${employee?.guid === item.guid && '!border-[#DFE4E8] text-[#242933]'}
                ${employee && (employee?.guid !== item.guid) && 'cursor-not-allowed'}
                `
              }
              onClick={() => handleConfirm(item)}
            >
              {employee?.guid === item.guid ? '已确认' : '确认'}
            </Button>
          )}
        </div>
      ))}
    </div>
  )
}

const ScheduleCard = ({ data, isEmbedMobile, isLast, onConfirm, onCancel }: ScheduleCardProps) => {
  const scheduleOrMeeting = localStorage.getItem('scheduleOrMeeting')
  const [selected, setSelected] = useState<Employee[]>([])
  const [isCancel, setIsCancel] = useState(false)
  const allowSubmit = !!selected.length
  const { appData } = useChatWithHistoryContext()
  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const scheduleBg = chatPageConfigData?.scheduleBg || '#ECECFF'
  const scheduleTitleColor = chatPageConfigData?.scheduleTitleColor || '#6550CE'

  const handleItemSelect = (item: Employee) => {
    selected.push(item)
    setSelected([...selected])
  }

  const handleCreate = () => {
    const confirmData = scheduleOrMeeting === 'schedule'
      ? {
        summary: data.summary,
        description: data.description,
        start_time: data.start_time,
        end_time: data.end_time,
        event: 'create_calendar_submit',
        attendees_id: selected.map(item => item.guid).join(','),
        attendees_name: selected.map(item => item.fullname).join(','),
      }
      : scheduleOrMeeting === 'meeting'
        ? {
          title: data.summary,
          meeting_start: data.start_time,
          meeting_end: data.end_time,
          create_meeting_submit: 'create_meeting_success',
          attendees_ids: selected.map(item => item.guid).join(','),
          attendees_names: selected.map(item => item.fullname).join(','),
          meeting_location: data?.meeting_location,
        }
        : {}
    onConfirm?.({ ...confirmData })
  }

  const handleCancel = () => {
    setIsCancel(true)
    onCancel?.()
  }

  const typeTit = scheduleOrMeeting === 'schedule' ? INTENT_INCLUDES_SCHEDULE : scheduleOrMeeting === 'meeting' ? INTENT_INCLUDES_MEETING : ''
  return (
    <div className="w-full bg-[#fff] rounded-[8px] overflow-auto">
      <div
        className={`text-[18px] font-medium py-[16px] px-[24px] ${isEmbedMobile && '!text-[14px] !p-[12px]'}`}
        style={{ background: scheduleBg, color: scheduleTitleColor }}
      >{`请确认${typeTit}参与人员`}</div>
      {
        data.employees?.map((item: { keyword: string; employees: Employee[] }, index: number) => (
          <List data={item} isEmbedMobile={isEmbedMobile} scheduleTitleColor={scheduleTitleColor} hideBtn={!isLast || isCancel} key={index} onSelected={handleItemSelect} />
        ))
      }
      {(isLast && !isCancel) && (
        <div className="flex justify-end gap-[8px] px-[24px] items-center h-[64px] border-t-[1px] border-t-[#EEF0F2]">
          <Button className='rounded-[4px] px-[18px] h-[32px]' onClick={handleCancel}>
            取消
          </Button>
          <Button
            className={`rounded-[4px] px-[18px] h-[32px] ${!allowSubmit ? 'bg-[#c0c0c0] cursor-not-allowed hover:!bg-[#c0c0c0]' : 'text-[#fff]'}`}
            // variant="primary"
            style={{ background: allowSubmit ? 'linear-gradient(270deg, #7D67FF 0%, #5099FF 100%)' : '' }}
            onClick={() => allowSubmit && handleCreate()}
          >
            创建
          </Button>
        </div>
      )}
    </div>
  )
}

export default ScheduleCard
