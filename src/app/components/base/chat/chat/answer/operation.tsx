import type { FC } from 'react'
import {
  memo,
  useMemo,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import {
  RiClipboardLine,
  RiResetLeftLine,
  RiThumbDownLine,
  RiThumbUpLine,
} from '@remixicon/react'
import type { ChatItem } from '../../types'
import { useChatContext } from '../context'
import copy from 'copy-to-clipboard'
import Toast from '@/app/components/base/toast'
import AnnotationCtrlButton from '@/app/components/base/features/new-feature-panel/annotation-reply/annotation-ctrl-button'
import EditReplyModal from '@/app/components/app/annotation/edit-annotation-modal'
import Log from '@/app/components/base/chat/chat/log'
import ActionButton, { ActionButtonState } from '@/app/components/base/action-button'
import NewAudioButton from '@/app/components/base/new-audio-button'
import cn from '@/utils/classnames'
import type { EmbedSource } from '@/models/share'
import Tooltip from '../../../tooltip'

type OperationProps = {
  item: ChatItem
  question: string
  index: number
  showPromptLog?: boolean
  maxSize: number
  contentWidth: number
  hasWorkflowProcess: boolean
  noChatInput?: boolean
  embedSource?: EmbedSource
  isMobile?: boolean
  className?: string
}
const Operation: FC<OperationProps> = ({
  item,
  question,
  index,
  showPromptLog,
  maxSize,
  contentWidth,
  hasWorkflowProcess,
  noChatInput,
  embedSource,
  isMobile,
  className,
}) => {
  const { t } = useTranslation()
  const {
    config,
    onAnnotationAdded,
    onAnnotationEdited,
    onAnnotationRemoved,
    onFeedback,
    onRegenerate,
  } = useChatContext()
  const [isShowReplyModal, setIsShowReplyModal] = useState(false)
  const {
    id,
    isOpeningStatement,
    content: messageContent,
    annotation,
    feedback,
    adminFeedback,
    agent_thoughts,
    isPrompt,
  } = item

  const hasAnnotation = !!annotation?.id
  const isEmbedMobile = embedSource && isMobile
  const [localFeedback, setLocalFeedback] = useState(config?.supportAnnotation ? adminFeedback : feedback)

  const content = useMemo(() => {
    if (agent_thoughts?.length)
      return agent_thoughts.reduce((acc, cur) => acc + cur.thought, '')

    return messageContent
  }, [agent_thoughts, messageContent])

  const handleFeedback = async (rating: 'like' | 'dislike' | null) => {
    if (!config?.supportFeedback || !onFeedback)
      return

    await onFeedback?.(id, { rating })
    setLocalFeedback({ rating })
  }

  const operationWidth = useMemo(() => {
    let width = 0
    if (!isOpeningStatement)
      width += 26
    if (!isOpeningStatement && showPromptLog)
      width += 28 + 8
    if (!isOpeningStatement && config?.text_to_speech?.enabled)
      width += 26
    if (!isOpeningStatement && config?.supportAnnotation && config?.annotation_reply?.enabled)
      width += 26
    if (config?.supportFeedback && !localFeedback?.rating && onFeedback && !isOpeningStatement)
      width += 60 + 8
    if (config?.supportFeedback && localFeedback?.rating && onFeedback && !isOpeningStatement)
      width += 28 + 8
    return width
  }, [isOpeningStatement, showPromptLog, config?.text_to_speech?.enabled, config?.supportAnnotation, config?.annotation_reply?.enabled, config?.supportFeedback, localFeedback?.rating, onFeedback])

  const positionRight = useMemo(() => operationWidth < maxSize, [operationWidth, maxSize])

  // if (embedSource) {
  //   return <div className='mt-[17px] flex items-center justify-end gap-[6px]'>
  //     {
  //       !isOpeningStatement && config?.text_to_speech?.enabled && (
  //         <AudioBtn
  //           id={id}
  //           value={content}
  //           noCache={false}
  //           voice={config?.text_to_speech?.voice}
  //           embedSource={embedSource}
  //         />
  //       )
  //     }

  //     {!isOpeningStatement && (
  //       <CopyBtn
  //         embedSource={embedSource}
  //         isPlain={true}
  //         value={content}
  //       />
  //     )}

  //     <div className="h-[18px] w-[1px] bg-[#DFE4E8]"></div>

  //      {
  //       !isOpeningStatement && <div className="flex">
  //         {
  //           config?.supportFeedback && onFeedback && !isOpeningStatement && (
  //             <div className='flex gap-[6px] items-center ml-1 shrink-0'>
  //               <Tooltip
  //                 popupContent={t('appDebug.operation.agree')}
  //               >
  //                 <div
  //                   className='flex justify-center items-center w-6 h-6 rounded-md cursor-pointer hover:bg-black/5 hover:text-gray-800'
  //                   onClick={() => handleFeedback(localFeedback?.rating === 'like' ? null : 'like')}
  //                 >
  //                   <Image src={localFeedback?.rating === 'like' ? LarkLikeActive : LarkLike} alt="" />
  //                 </div>
  //               </Tooltip>
  //               <Tooltip
  //                 popupContent={t('appDebug.operation.disagree')}
  //               >
  //                 <div
  //                   className='flex justify-center items-center w-6 h-6 rounded-md cursor-pointer hover:bg-black/5 hover:text-gray-800'
  //                   onClick={() => handleFeedback(localFeedback?.rating === 'dislike' ? null : 'dislike')}
  //                 >
  //                   <Image src={localFeedback?.rating === 'dislike' ? LarkDislikeActive : LarkDislike} alt="" />
  //                 </div>
  //               </Tooltip>
  //             </div>
  //           )
  //         }
  //        </div>
  //      }
  //   </div>
  // }

  return (
    <>
      <div
        className={cn(
          'absolute flex justify-end gap-1',
          hasWorkflowProcess && '-bottom-4 right-2',
          !positionRight && '-bottom-4 right-2',
          !hasWorkflowProcess && positionRight && '!top-[9px]',
          embedSource && 'relative items-center',
          className,
        )}
        style={(!hasWorkflowProcess && positionRight && !embedSource) ? { left: contentWidth + 8 } : {}}
      >
        {showPromptLog && !isOpeningStatement && !isPrompt && (
          <div className={cn(embedSource ? 'block' : 'hidden group-hover:block')}>
            <Log logItem={item} />
          </div>
        )}
        {!isOpeningStatement && (
          <div className={cn(embedSource ? 'flex' : 'ml-1 hidden items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex')}>
            {(config?.text_to_speech?.enabled && !isPrompt) && (
              <NewAudioButton
                id={id}
                value={content}
                voice={config?.text_to_speech?.voice}
              />
            )}
            <Tooltip
              popupContent={'复制'}
            >
              <ActionButton onClick={() => {
                copy(content)
                Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') })
              }}>
                <RiClipboardLine className='h-4 w-4' />
              </ActionButton>
            </Tooltip>

            {!noChatInput && !isPrompt && (
              <Tooltip
                popupContent={'重新生成'}
              >
                <ActionButton onClick={() => onRegenerate?.(item)}>
                  <RiResetLeftLine className='h-4 w-4' />
                </ActionButton>
              </Tooltip>
            )}
            {(config?.supportAnnotation && config.annotation_reply?.enabled) && (
              <AnnotationCtrlButton
                appId={config?.appId || ''}
                messageId={id}
                cached={!!annotation?.id}
                query={question}
                answer={content}
                onAdded={(id, authorName) => onAnnotationAdded?.(id, authorName, question, content, index)}
                onEdit={() => setIsShowReplyModal(true)}
              />
            )}
          </div>
        )}
        {embedSource && !isPrompt && <div className="h-[16px] w-[1px] bg-[#DFE4E8]"></div>}
        {!isOpeningStatement && config?.supportFeedback && !localFeedback?.rating && onFeedback && !isPrompt && (
          <div className={cn(embedSource ? 'flex' : 'ml-1 hidden items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex')}>
            {!localFeedback?.rating && (
              <>
                <Tooltip
                  popupContent={'赞同'}
                >
                  <ActionButton onClick={() => handleFeedback('like')}>
                    <RiThumbUpLine className='h-4 w-4' />
                  </ActionButton>
                </Tooltip>
                <Tooltip
                  popupContent={'反对'}
                >
                  <ActionButton onClick={() => handleFeedback('dislike')}>
                    <RiThumbDownLine className='h-4 w-4' />
                  </ActionButton>
                </Tooltip>
              </>
            )}
          </div>
        )}
        {!isOpeningStatement && config?.supportFeedback && localFeedback?.rating && onFeedback && !isPrompt && (
          <div className={cn(embedSource ? '' : 'ml-1 flex items-center gap-0.5 rounded-[10px] border-[0.5px] border-components-actionbar-border bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm')}>
            {localFeedback?.rating === 'like' && (
              <Tooltip
                popupContent={'取消赞同'}
              >
                <ActionButton state={ActionButtonState.Active} onClick={() => handleFeedback(null)}>
                  <RiThumbUpLine className='h-4 w-4' />
                </ActionButton>
              </Tooltip>
            )}
            {localFeedback?.rating === 'dislike' && (
              <Tooltip
              popupContent={'取消反对'}
            >
              <ActionButton state={ActionButtonState.Destructive} onClick={() => handleFeedback(null)}>
                <RiThumbDownLine className='h-4 w-4' />
              </ActionButton>
            </Tooltip>
            )}
          </div>
        )}
      </div>
      <EditReplyModal
        isShow={isShowReplyModal}
        onHide={() => setIsShowReplyModal(false)}
        query={question}
        answer={content}
        onEdited={(editedQuery, editedAnswer) => onAnnotationEdited?.(editedQuery, editedAnswer, index)}
        onAdded={(annotationId, authorName, editedQuery, editedAnswer) => onAnnotationAdded?.(annotationId, authorName, editedQuery, editedAnswer, index)}
        appId={config?.appId || ''}
        messageId={id}
        annotationId={annotation?.id || ''}
        createdAt={annotation?.created_at}
        onRemove={() => onAnnotationRemoved?.(index)}
      />
    </>
  )
}

export default memo(Operation)
