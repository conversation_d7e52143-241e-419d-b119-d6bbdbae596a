import type {
  FC,
  ReactNode,
} from 'react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import type {
  ChatConfig,
  ChatItem,
} from '../../types'
import Button from '../../../button'
import { INTENT_INCLUDES_SCHEDULE, NEED_CREATE_MEETING } from '../u-const'
import Operation from './operation'
import AgentContent from './agent-content'
import BasicContent from './basic-content'
import SuggestedQuestions from './suggested-questions'
import More from './more'
import WorkflowProcessItem from './workflow-process'
import ScheduleCard from './schedule-card'
import styles from './index.module.css'
import Spinner from '@/app/components/base/spinner'
import LoadingAnim from '@/app/components/base/chat/chat/loading-anim'
import Citation from '@/app/components/base/chat/chat/citation'
import { EditTitle } from '@/app/components/app/annotation/edit-annotation-modal/edit-item'
import type { AppData, EmbedSource } from '@/models/share'
import AnswerIcon from '@/app/components/base/answer-icon'
import cn from '@/utils/classnames'
import { FileList } from '@/app/components/base/file-uploader'
import ContentSwitch from '../content-switch'
import { downloadFile } from '@/utils/index'
import { fetchAiPptExportApi } from '@/service/share'
import AnnotationContent from './annotation-content'

type SvgProps = {
  className?: string
}
const IconPptBtnLeft = ({ className }: SvgProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" className={className} viewBox="0 0 1024 1024">
    <path d="M40.96 168.96c0-25.6 20.48-40.96 40.96-40.96h855.04c25.6 0 40.96 20.48 40.96 40.96 0 25.6-20.48 40.96-40.96 40.96H87.04c-25.6 5.12-46.08-15.36-46.08-40.96"></path>
    <path d="M128 168.96c0-25.6 20.48-40.96 40.96-40.96h680.96c25.6 0 40.96 20.48 40.96 40.96v552.96c0 25.6-20.48 40.96-40.96 40.96H168.96c-25.6 0-40.96-20.48-40.96-40.96zm87.04 46.08v471.04h599.04V215.04z"></path>
    <path d="M440.32 312.32c15.36-15.36 46.08-15.36 61.44 0l107.52 107.52c15.36 15.36 15.36 46.08 0 61.44L501.76 588.8c-15.36 15.36-46.08 15.36-61.44 0s-15.36-46.08 0-61.44l76.8-76.8-76.8-76.8c-15.36-20.48-15.36-46.08 0-61.44m40.96 384c15.36-15.36 46.08-15.36 61.44 0l168.96 168.96c15.36 15.36 15.36 46.08 0 61.44s-46.08 15.36-61.44 0L512 783.36 373.76 921.6c-15.36 15.36-46.08 15.36-61.44 0s-15.36-46.08 0-61.44z"></path>
  </svg>

)
const IconpPptbtnRight = ({ className }: SvgProps) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" className={className} viewBox="0 0 1024 1024">
    <path d="M353.849 798.151a42.667 42.667 0 0 1 0-60.302L579.698 512l-225.85-225.849a42.667 42.667 0 1 1 60.303-60.302l256 256a42.667 42.667 0 0 1 0 60.302l-256 256a42.667 42.667 0 0 1-60.302 0"></path>
  </svg>
)

type AnswerProps = {
  item: ChatItem
  ppt: any
  question: string
  index: number
  config?: ChatConfig
  answerIcon?: ReactNode
  responding?: boolean
  showPromptLog?: boolean
  chatAnswerContainerInner?: string
  hideProcessDetail?: boolean
  appData?: AppData
  noChatInput?: boolean
  switchSibling?: (siblingMessageId: string) => void
  isLast?: boolean
  embedSource?: EmbedSource
  isMobile?: boolean
  hideOperator?: boolean
  onNewTopic?: () => void
  onOneKeyGenerationPpt?: () => void
  onOneKeyGenerationMeeting?: (data: any) => void
  onEditClick?: () => void
  onCreateSchedule?: (data: any) => void
  updateIntent?: (data: any) => void
  onSelectIntent?: (data: { question: string; intent: string }) => void
  activeIntent?: string
  generationPptLoading?: boolean
}

const Answer: FC<AnswerProps> = ({
  item,
  ppt,
  question,
  index,
  config,
  answerIcon,
  responding,
  showPromptLog,
  chatAnswerContainerInner,
  hideProcessDetail,
  appData,
  noChatInput,
  switchSibling,
  onNewTopic,
  onOneKeyGenerationPpt,
  onOneKeyGenerationMeeting,
  onEditClick,
  isLast,
  embedSource,
  isMobile,
  onCreateSchedule,
  hideOperator,
  updateIntent,
  onSelectIntent,
  activeIntent,
  generationPptLoading,
}) => {
  const { t } = useTranslation()
  const {
    content,
    citation,
    agent_thoughts,
    more,
    annotation,
    workflowProcess,
    allFiles,
    message_files,
    intent,
    llm, // 大模型
    frontTip, // 前置提示
    behindTip, // 后置提示
    isPrompt, // 是否提示词（不展示工具栏）
    intents = [], // 意图识别选项
    meetingJsonData = {}, // 日程同步创建会议
    displayType,
  } = item
  // 显示工作流
  const { show_workflow_steps } = appData?.site ?? {}
  const { id: pptId, taskId, thumbnail } = ppt
  const mcpData = workflowProcess?.tracing?.filter(item => item.execution_metadata) || []

  const hasAgentThoughts = !!agent_thoughts?.length
  const isEmbedMobile = embedSource && isMobile

  const [containerWidth, setContainerWidth] = useState(0)
  const [contentWidth, setContentWidth] = useState(0)
  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const scheduleData = useRef<any>()
  const [downLoading, setDownLoading] = useState(false)
  const [downPdfLoading, setDownPdfLoading] = useState(false)

  try {
    const data = JSON.parse(content)
    if (data.employees)
      scheduleData.current = data
  }
  catch (err) {
    // console.log(err)
  }
  const getContainerWidth = () => {
    if (containerRef.current)
      setContainerWidth(containerRef.current?.clientWidth + 16)
  }
  useEffect(() => {
    getContainerWidth()
  }, [])

  const getContentWidth = () => {
    if (contentRef.current)
      setContentWidth(contentRef.current?.clientWidth)
  }

  const handleScheduleCancel = () => {
    updateIntent?.('')
  }

  useEffect(() => {
    if (!responding)
      getContentWidth()
  }, [responding])

  const handlePptDowmload = async (ppt: any, fileType: string) => {
    fileType === 'ppt' ? setDownLoading(true) : setDownPdfLoading(true)
    const exportParms = {
      id: ppt?.id,
      format: fileType,
      edit: 'true',
      files_to_zip: 'true',
    }
    const res: any = await fetchAiPptExportApi(exportParms)
    const dowmloadUrl = res?.data?.[0]
    dowmloadUrl && downloadFile(dowmloadUrl)
    fileType === 'ppt' ? setDownLoading(false) : setDownPdfLoading(false)
  }

  // Recalculate contentWidth when content changes (e.g., SVG preview/source toggle)
  useEffect(() => {
    if (!containerRef.current)
      return
    const resizeObserver = new ResizeObserver(() => {
      getContentWidth()
    })
    resizeObserver.observe(containerRef.current)
    return () => {
      resizeObserver.disconnect()
    }
  }, [])

  const NewTopicFooter = ({ intent }: { intent: string; onClick?: () => void }) => {
    return (
      <div className={`mt-[8px] flex items-center justify-center rounded-[8px] bg-[#EFF0F5] py-[5px] text-[12px] leading-[22px] text-[#5C6574]  ${isMobile && 'px-[20px]'}`}>
        <p>已进入<span className='text-[#3B67FF]'>[{intent}]</span>场景，可点击对话框上方按钮切换至其他场景。</p>
      </div>
    )
  }
  // 是否显示一键生成PPT按钮
  const hideOneKeyGenerationPptBtn = (content.length > 300 && (content.includes('3.') || content.includes('三、'))) || content.includes('```markdown')
  const PptGenerationFooter = ({ intent, btnTxt, onClick }: { intent: string; btnTxt?: string; onClick?: () => void }) => {
    return (
      (intent.includes('PPT') && hideOneKeyGenerationPptBtn)
        ? (
          <div className="mt-[8px] flex">
            <Button className='h-[38px] cursor-pointer rounded-[10px] border-[#3B67FF] text-[14px] text-[#3B67FF] shadow-none' onClick={onClick}>
              {
                generationPptLoading
                  ? <> PPT生成中 <Spinner loading={generationPptLoading} className='!ml-1 !h-3 !w-3 !border-2 !text-[#3B67FF]' /></>
                  : <><IconPptBtnLeft className='mr-[4px] h-[20px] w-[20px]'/>{btnTxt || '一键生成PPT'}<IconpPptbtnRight className='ml-[4px] h-[20px] w-[20px]' /></>
              }

            </Button>
          </div>
        )
        : null
    )
  }
  const MeetingGenerationFooter = ({ intent, btnTxt, onClick }: { intent: string; btnTxt?: string; onClick?: () => void }) => {
    return (
      intent.includes(INTENT_INCLUDES_SCHEDULE) && <div className="mt-[8px] flex">
        <Button className='h-[38px] cursor-pointer rounded-[10px] border-[#3B67FF] text-[14px] text-[#3B67FF] shadow-none' onClick={onClick}>
          {btnTxt || '同步预定腾讯会议'}<IconpPptbtnRight className='ml-[4px] h-[20px] w-[20px]' />
        </Button>
      </div>

    )
  }
  const PptReslutShow = ({ thumbnail, onClick, onEditClick, onPdfClick }: { thumbnail: string; onClick?: (data: any) => void; onEditClick?: () => void; onPdfClick?: (data: any) => void }) => {
    return (
      thumbnail
        ? (
          <div className="mt-[8px] flex w-[61%] flex-col">
            {thumbnail && <span style={{
              objectFit: 'contain',
              aspectRatio: '16 / 9',
              width: '100%',
              cursor: 'pointer',
            }} onClick={onEditClick}>
              <img src={thumbnail} alt="ppt" className="h-[100%] w-[100%] rounded-[4px]"/>
            </span>}
            <div className="mt-[8px] flex">
              <Button loading={downLoading} className='h-[38px] cursor-pointer rounded-[10px] bg-[#1e86ff]  text-[14px] text-[#ffffff] shadow-none hover:bg-[#1e86ff]' onClick={onClick}>
                下载PPT
              </Button>
              <Button loading={downPdfLoading} className='ml-[8px] h-[38px] cursor-pointer rounded-[10px] bg-[#1e86ff]  text-[14px] text-[#ffffff] shadow-none hover:bg-[#1e86ff]' onClick={onPdfClick}>
                下载PDF
              </Button>
              <Button className='ml-[8px] h-[38px] cursor-pointer rounded-[10px] text-[14px] shadow-none' onClick={onEditClick}>
                编辑PPT
              </Button>
            </div>
          </div>
        )
        : null
    )
  }

  const renderOperations = (className?: string) => (
    <Operation
      className={className}
      embedSource={embedSource}
      isMobile={isMobile}
      hasWorkflowProcess={!!workflowProcess}
      maxSize={containerWidth - contentWidth - 4}
      contentWidth={contentWidth}
      item={item}
      question={question}
      index={index}
      showPromptLog={showPromptLog}
      noChatInput={noChatInput}
    />
  )

  const handleSwitchSibling = useCallback((direction: 'prev' | 'next') => {
    if (direction === 'prev')
      item.prevSibling && switchSibling?.(item.prevSibling)
    else
      item.nextSibling && switchSibling?.(item.nextSibling)
  }, [switchSibling, item.prevSibling, item.nextSibling])

  return (
    <div>
      {frontTip && <div className='mx-auto mb-[30px] mt-[6px] w-fit min-w-[100px] rounded-[4px] bg-[#DFE4E8] px-[20px] py-[6px] text-center text-[12px] text-[#434B5B]'>{frontTip}</div>}
      <div className='mb-2 flex last:mb-0'>
        <div className='relative h-10 w-10 shrink-0'>
          {answerIcon || <AnswerIcon />}
          {responding && (
            <div className='absolute left-[-3px] top-[-3px] flex h-4 w-4 items-center rounded-full border-[0.5px] border-divider-subtle bg-background-section-burn pl-[6px] shadow-xs'>
              <LoadingAnim type='avatar' />
            </div>
          )}
        </div>
        <div className='chat-answer-container group ml-4 w-0 grow pb-4' ref={containerRef}>
          <div className={cn('group relative pr-10', chatAnswerContainerInner, isEmbedMobile && 'pr-[0px]')}>
            <div
              ref={contentRef}
              className={cn('body-lg-regular relative inline-block max-w-full rounded-2xl bg-chat-bubble-bg px-4 py-3 text-text-primary', workflowProcess && 'w-full')}
            >
              {
                (!responding && !embedSource) && renderOperations()
              }
              {/** Render the normal steps */}
              {
                workflowProcess && !hideProcessDetail && show_workflow_steps && (
                  <WorkflowProcessItem
                    data={workflowProcess}
                    item={item}
                    hideProcessDetail={hideProcessDetail}
                  />
                )
              }
              {/** Hide workflow steps by it's settings in siteInfo */}
              {
                workflowProcess && hideProcessDetail && show_workflow_steps && appData && (
                  <WorkflowProcessItem
                    data={workflowProcess}
                    item={item}
                    hideProcessDetail={hideProcessDetail}
                    readonly={!appData.site.show_workflow_steps}
                  />
                )
              }
              {
                responding && !content && !hasAgentThoughts && !mcpData.length && (
                  <div className='flex h-5 w-6 items-center justify-center'>
                    {embedSource ? <span className={styles['lark-loader']}></span> : <LoadingAnim type='text' />}
                  </div>
                )
              }
              {scheduleData.current ? (
                <ScheduleCard isEmbedMobile={Boolean(embedSource && isMobile)} isLast={isLast} data={scheduleData.current} onConfirm={onCreateSchedule} onCancel={handleScheduleCancel} />
              ) : displayType
              ? <AnnotationContent isMobile={isMobile} list={mcpData}/>
              : (
                <>

                  {
                    content && !hasAgentThoughts && (
                      <BasicContent item={item} />
                    )
                  }
                  {
                    (hasAgentThoughts) && (
                      <AgentContent
                        item={item}
                        responding={responding}
                        content={content}
                      />
                    )
                  }
                  {
                    !!allFiles?.length && !embedSource && (
                      <FileList
                        className='my-1'
                        files={allFiles}
                        showDeleteAction={false}
                        showDownloadAction
                        canPreview
                      />
                    )
                  }
                  {
                    !!message_files?.length && (
                      <FileList
                        className='my-1'
                        files={message_files}
                        showDeleteAction={false}
                        showDownloadAction
                        canPreview
                      />
                    )
                  }
                </>
               )}
              {
                annotation?.id && annotation.authorName && (
                  <EditTitle
                    className='mt-1'
                    title={t('appAnnotation.editBy', { author: annotation.authorName })}
                  />
                )
              }
              <SuggestedQuestions item={item} />
              {
                !!citation?.length && !responding && (
                  <Citation data={citation} showHitInfo={config?.supportCitationHitInfo} />
                )
              }
              {
                item.siblingCount && item.siblingCount > 1 && item.siblingIndex !== undefined && (
                  <ContentSwitch
                    count={item.siblingCount}
                    currentIndex={item.siblingIndex}
                    prevDisabled={!item.prevSibling}
                    nextDisabled={!item.nextSibling}
                    switchSibling={handleSwitchSibling}
                  />
                )
              }
              {!responding && activeIntent && isLast && <NewTopicFooter intent={activeIntent} onClick={onNewTopic} />}
              {!responding && activeIntent && isLast && !thumbnail && <PptGenerationFooter intent={activeIntent} onClick={onOneKeyGenerationPpt} />}
              {!responding && isLast && activeIntent && meetingJsonData?.event === NEED_CREATE_MEETING && <MeetingGenerationFooter intent={activeIntent} onClick={() => { onOneKeyGenerationMeeting?.(meetingJsonData) } } />}
              {thumbnail && <PptReslutShow
                thumbnail={thumbnail}
                onClick={() => { handlePptDowmload(ppt, 'ppt') }}
                onPdfClick={() => { handlePptDowmload(ppt, 'pdf') }}
                onEditClick={onEditClick} />}
              {
                // (!responding && embedSource && !hideOperator && !isPrompt && !intents.length) && renderOperations('mb-[10px]')
                (!responding && embedSource) && renderOperations('mb-[10px]')
              }
            </div>
            {!!intents?.length && <ul className="mb-[20px] mt-[10px] flex gap-x-[10px] pr-14">
              {intents.map((intent, index) => (
                <li className={`flex h-[32px] cursor-pointer items-center rounded-[16px] border bg-[#fff] px-[10px] text-[14px] ${intent === activeIntent ? 'bg-gradient-to-r from-[#5099FF] to-[#7D67FF] text-[#ffffff]' : ''}` } key={index}
                    onClick={() => { onSelectIntent?.({ question, intent }) }}>{intent}</li>
              ))}
            </ul>}
            {!intents?.length && <More more={more} />}
          </div>
        </div>
      </div>
      {behindTip && <div className='mx-auto mb-[30px] mt-[10px] w-fit min-w-[100px] rounded-[4px] bg-[#DFE4E8] px-[20px] py-[6px] text-center text-[12px] text-[#434B5B]'>{behindTip}</div>}
    </div>
  )
}

export default Answer

// export default memo(Answer, (prevProps, nextProps) =>
//   prevProps.responding === false && nextProps.responding === false && prevProps.activeIntent === nextProps.activeIntent,
// )
