import { RightSidebarTrigger } from '../../chat-with-history/right-sidebar'
import BasicContent from './basic-content'

type Props = {
    list: any[],
    isMobile?: boolean
}

const AnnotationContent = ({ list, isMobile }: Props) => {
    return (
        <div>
            {list.map((item) => {
               const temp = item?.execution_metadata?.agent_log?.[0]

               return <RightSidebarTrigger process={temp.process} status={temp.status} isMobile={isMobile} references={temp.references}>
                    <BasicContent item={{
                        content: temp?.content || '',
                    }} />
                </RightSidebarTrigger>
            })}
        </div>
    )
}

export default AnnotationContent
