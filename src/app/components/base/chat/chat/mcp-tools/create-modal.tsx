import Modal from '@/app/components/base/modal'
import Input from '@/app/components/datasets/create/website/base/input'
import Textarea from '@/app/components/base/textarea'
import Image from '@/app/components/base/image'
import { useRef } from 'react'
import IconTool from '@/assets/tool.svg'
import { PortalSelect } from '@/app/components/base/select'

// import * as Yup from 'yup'

// const schema = Yup.object().shape({
//     username: Yup.string()
//       .min(3, '用户名至少3个字符')
//       .max(20, '用户名不能超过20个字符')
//       .required('用户名必填'),
//     email: Yup.string()
//       .email('请输入有效的邮箱地址')
//       .required('邮箱必填'),
//     age: Yup.number()
//       .typeError('年龄必须是数字')
//       .integer('年龄必须是整数')
//       .min(18, '年龄必须大于18岁')
//       .max(100, '年龄必须小于100岁'),
//     password: Yup.string()
//       .min(8, '密码至少8个字符')
//       .matches(
//         /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
//         '密码必须包含大小写字母和数字',
//       ),
//     confirmPassword: Yup.string()
//       .oneOf([Yup.ref('password'), null], '密码必须匹配')
//       .required('请确认密码'),
//   })

// 传输方式
const responseType = ['SSE', 'HTTPStreaming']

const CreateModal = ({ isShow, onClose }) => {
    const uploadRef = useRef<HTMLInputElement>(null)
    const tempUrl = useRef('')

    const handleUpload = () => {
        uploadRef.current?.click()
    }

    const handleFileChange = (e) => {
        const file = e.target.files[0]

        tempUrl.current = URL.createObjectURL(file)

        if (uploadRef.current)
            uploadRef.current.value = ''
    }

    return <Modal isShow={isShow} title="新增MCP工具" closable onClose={onClose}>
        <div className='mb-4'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>图标 <span className='ml-1 text-red-500'>*</span></div>
            <div className="flex items-center">
                <Image src={tempUrl.current || IconTool} width="28" height="28" alt=""/>
                <span className="ml-[10px] cursor-pointer text-[14px] text-[#3B67FF]" onClick={handleUpload}>重新上传</span>
                <input ref={uploadRef} accept='image/*' className="hidden" type="file" onChange={handleFileChange}></input>
            </div>
        </div>
        <div className='mb-4'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>名称 <span className='ml-1 text-red-500'>*</span></div>
            <Input value={''} placeholder='请输入服务名称' onChange={() => {}}/>
        </div>
        <div className='mb-4'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>描述 <span className='ml-1 text-red-500'>*</span></div>
            <Textarea value={''} placeholder='请描述该服务的作用' onChange={() => {}}/>
        </div>
        <div className='mb-4'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>请求地址 <span className='ml-1 text-red-500'>*</span></div>
            <Input value={''} placeholder='连接服务的URL' onChange={() => {}}/>
        </div>
        <div className='mb-4'>
            <div className='py-2 text-sm font-medium leading-[20px] text-text-primary'>传输方式 <span className='ml-1 text-red-500'>*</span></div>
            <PortalSelect
                value={responseType[0]}
                onSelect={(i) => { }}
                popupClassName={'min-w-[200px] z-[999]'}
                items={(responseType || []).map(i => ({ name: i, value: i }))}
            />
        </div>
    </Modal>
}

export default CreateModal
