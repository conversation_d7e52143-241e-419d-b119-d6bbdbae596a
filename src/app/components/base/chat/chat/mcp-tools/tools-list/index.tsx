import Loading from '@/app/components/base/loading'
import type { McpToolsData } from '@/models/share'
import Image from '@/app/components/base/image'
import type { ReactElement } from 'react'
import useSWR from 'swr'

const ToolsList = ({ apiKey, api, extra }: { apiKey: string, api: () => Promise<{ data: McpToolsData[] }>, extra?: (data: McpToolsData, reload: () => void) => ReactElement }) => {
    const { data, isLoading, mutate } = useSWR(apiKey, api)

    if(isLoading) return <Loading className='!h-[300px]' />

    return (
        <ul className='h-[300px] overflow-scroll'>
            {
                data?.data?.map(item => (
                    <li className='mb-[10px] flex items-center rounded-[4px] p-[10px] hover:bg-[#f5f6f7]'>
                        <Image src={item.icon} width='32' height="32" alt="" />
                        <div className="ml-[10px] flex-1 overflow-hidden">
                            <h2 className='overflow-hidden text-ellipsis text-nowrap text-[14px]' title={item.name}>{item.name}</h2>
                            <p className='overflow-hidden text-ellipsis text-nowrap text-[12px] leading-normal text-[#646a73]' title={item.description}>{item.description}</p>
                        </div>
                        <div>
                            {extra?.(item, mutate)}
                        </div>
                    </li>
                ))
            }
        </ul>
    )
}

export default ToolsList
