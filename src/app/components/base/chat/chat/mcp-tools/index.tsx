import { RiArrowDownSLine } from '@remixicon/react'
import Image from '@/app/components/base/image'
import Tooltip from '@/app/components/base/tooltip'
import { useMemo, useState } from 'react'
import {
  FloatingPortal,
  flip,
  offset,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from '@floating-ui/react'
import useSWR from 'swr'
import { fetchUserMcpTools, updateMcpStatus } from '@/service/share'
import type { McpToolsData } from '@/models/share'
import Switch from '@/app/components/base/switch'

import IconTools from '@/assets/tools.svg'
import CreateModal from './create-modal'
import ToolsManageModal from './tools-manage-modal'

type Props = {
  isMobile: boolean
}

const McpTools = ({ isMobile }: Props) => {
  const [isOpen, setIsOpen] = useState(false)
  const [visible, setVisible] = useState({ manage: false, create: false })

  const { data, isLoading, mutate } = useSWR('user-mcp-tools', fetchUserMcpTools)
  const mcpTools = useMemo(() => data?.data || [], [data])

  const { refs, floatingStyles, context } = useFloating({
    placement: 'top-start',
    open: isOpen,
    onOpenChange: (bool: boolean) => setIsOpen(bool),
    middleware: [offset(10), flip(), shift()],
  })
  const click = useClick(context)
  const role = useRole(context)
  const dismiss = useDismiss(context)
  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    role,
    dismiss,
  ])

  const handleSwitchChange = (tool: McpToolsData, bool: boolean) => {
    const params = {
        tool_id: tool.id,
        enabled: bool,
    }
    updateMcpStatus(params)
  }

  return (
    <div>
      <Tooltip popupContent="MCP工具">
        <div>
            <div
            ref={refs.setReference}
            {...getReferenceProps()}
            className={
                `flex h-9 cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]
                ${isMobile && 'h-[48px] min-w-fit rounded-[24px] bg-[#fff] px-[10px]'}
            `}
            >
            <Image src={IconTools} width={24} height={24} alt="icon"></Image>
            <RiArrowDownSLine
                className={`min-w-5 text-[#A3AFBB] ${isMobile && 'w-1'}`}
            ></RiArrowDownSLine>
            </div>
        </div>
      </Tooltip>

      {isOpen && (
        <FloatingPortal>
          <div
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
            className="model-body z-50 min-w-44 border border-gray-200"
          >
            <div className="mb-[10px] flex justify-between px-2 text-[12px] !text-[#8f959e]">
                MCP工具
                {/* <span className='cursor-pointer text-[#3B67FF]' onClick={() => setVisible(pre => ({ ...pre, manage: true }))}>管理</span> */}
            </div>
            {mcpTools?.map((tool: McpToolsData, index) => (
                <div
                    key={index}
                    className={'model-item mb-2 flex cursor-pointer items-center rounded-lg px-2 py-1'}
                    >
                    {
                    tool.icon && <Image className='mr-3' src={tool.icon} width={22} height={22} alt='icon'></Image>
                    }
                    <div className='mr-[20px] min-w-[100px] flex-1'>
                        <Tooltip popupContent={tool.description}>
                          <div className="model-item-text whitespace-nowrap text-sm">{tool.name}</div>
                        </Tooltip>
                    </div>
                    <Switch
                      className='rounded-[8px]'
                      circleClassName="rounded-full"
                      defaultValue={tool.enabled}
                      onChange={bool => handleSwitchChange(tool, bool)}
                      size='md'
                    />
                </div>
            ))}
            {!mcpTools.length && !isLoading && <div className="text-center text-[12px]">暂无可用工具</div>}
          </div>
        </FloatingPortal>
      )}

      <ToolsManageModal isShow={visible.manage} isMobile={isMobile} onRefresh={mutate} onClose={() => setVisible(pre => ({ ...pre, manage: false }))}></ToolsManageModal>

      <CreateModal isShow={visible.create} onClose={() => setVisible(pre => ({ ...pre, create: false }))}></CreateModal>
    </div>
  )
}

export default McpTools
