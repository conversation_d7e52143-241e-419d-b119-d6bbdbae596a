import cn from 'classnames'

type Item = {
  key: string;
  label: string;
  children: any;
}

type Props = {
  activeKey: string;
  items: Item[];
  onChange: (key: string) => void;
}
const Tabs = ({ activeKey, items, onChange }: Props) => {
  return (
    <div>
      <ul className="flex rounded-[4px] bg-[#f5f6f7] p-[2px]">
        {items?.map(item => (
          <li
            className={cn(
              'flex-1 cursor-pointer py-[4px] text-center text-[14px] text-[#646a73] hover:bg-[#e4e6e7]',
              activeKey === item.key && 'rounded-[4px] !bg-white text-black',
            )}
            onClick={() => onChange?.(item.key)}
          >
            {item.label}
          </li>
        ))}
      </ul>
      <div className="mt-[10px]">{items?.find(item => item.key === activeKey)?.children}</div>
    </div>
  )
}

export default Tabs
