import Modal from '@/app/components/base/modal'
import Tabs from './tabs'
import ToolsList from './tools-list'
import {
  fetchMcpTools,
  fetchUserMcpTools,
  removeUserMcpTool,
  updateMcpStatus,
} from '@/service/share'
import { useState } from 'react'
import cn from 'classnames'
import Button from '@/app/components/base/button'
import type { McpToolsData } from '@/models/share'

type Props = {
  isShow: boolean;
  isMobile: boolean;
  onClose: () => void;
  onRefresh: () => void;
}

const BtnDelete = ({ isHover, item, disabled, reload }: { isHover: boolean, item: McpToolsData, disabled?: boolean, reload: () => void }) => {
  const [loading, setLoading] = useState(false)
  const toggleClass = (isHover || loading) && !item.default_enabled && !disabled

  const handleRemove = async () => {
      try {
          setLoading(true)

          const res = await removeUserMcpTool({ tool_id: item.id })

          res?.success && reload?.()
      }
      finally {
          setLoading(false)
      }
  }

  return <Button
    className={cn('w-[90px] rounded-[4px] px-[20px]', toggleClass && 'border-[1px] border-[#f54a45] text-[#f54a45] hover:bg-[#fee3e2]')}
    variant={toggleClass ? 'ghost' : 'secondary'}
    spinnerClassName={toggleClass ? '!text-[#f54a45]' : ''}
    loading={loading}
    disabled={item.default_enabled || disabled}
    onClick={() => handleRemove()}
  >
    {toggleClass ? '移除' : '已添加'}
  </Button>
}

const MarketExtra = ({ item, isMobile, reload }: { item: McpToolsData, isMobile: boolean, reload: () => void }) => {
  const [isHover, setIsHover] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleAdd = async (item: McpToolsData) => {
    try {
        setLoading(true)

        const res = await updateMcpStatus({ tool_id: item.id, enabled: true })

        res?.success && reload?.()
    }
    finally {
        setLoading(false)
    }
  }

  return item.added_status ? (
    <div
      onMouseOver={() => setIsHover(true)}
      onMouseLeave={() => setIsHover(false)}
    >
      <BtnDelete disabled={isMobile} isHover={isHover} item={item} reload={reload}/>
    </div>
  ) : (
    <Button
      className="w-[90px] rounded-[4px] bg-[#3B67FF] px-[20px] hover:bg-[#3B67FF]"
      variant="primary"
      onClick={() => handleAdd(item)}
      loading={loading}
      disabled={item.default_enabled}
    >
      添加
    </Button>
  )
}

const ToolsManageModal = ({ isShow, isMobile, onRefresh, onClose }: Props) => {
  const [activeTab, setActiveTab] = useState('market')

  const tabsItems = [
    {
      key: 'market',
      label: '服务市场',
      children: (
        <ToolsList
          apiKey="market"
          api={fetchMcpTools}
          extra={(item, reload) => <MarketExtra isMobile={isMobile} item={item} reload={() => {
            reload()
            onRefresh()
          }}/>}
        />
      ),
    },
    {
      key: 'added',
      label: '已添加',
      children: (
        <ToolsList
          apiKey="added"
          api={fetchUserMcpTools}
          extra={(item, reload) => <BtnDelete isHover item={item} reload={() => {
            reload()
            onRefresh()
          }}/>}
        />
      ),
    },
  ]

  return (
    <Modal
      title={
        <div className="mb-[30px] flex items-center justify-between pr-[30px]">
          服务管理
        </div>
      }
      isShow={isShow}
      onClose={onClose}
      closable
    >
      {/* <div className="my-[10px] flex justify-end">
        <span className="cursor-pointer text-[12px] text-[#3B67FF]">
          新增自定义服务
        </span>
      </div> */}
      <Tabs
        items={tabsItems}
        activeKey={activeTab}
        onChange={key => setActiveTab(key)}
      ></Tabs>
    </Modal>
  )
}

export default ToolsManageModal
