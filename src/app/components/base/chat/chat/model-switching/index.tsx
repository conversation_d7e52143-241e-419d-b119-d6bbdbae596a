import React, { useEffect, useMemo, useState } from 'react'
import './style.css'
import {
  FloatingPortal,
  flip,
  offset,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
  useRole,
} from '@floating-ui/react'
import Image from '@/app/components/base/image'
import { RiArrowDownSLine } from '@remixicon/react'
import Tooltip from '@/app/components/base/tooltip'

/** 单条数据type */
type ModelsType = {
  value: string
  label: string
  icon: string
}

/** 组件传入参数 */
type ModelTooltipProps = {
  models: any[]
  item?: ModelsType
  options?: ModelsType[]
  isMobile?: boolean
  showBuiltIn?: boolean
  placement?: 'top' | 'top-start' | 'top-end' | 'bottom' | 'bottom-start' | 'bottom-end' | 'right' | 'right-start' | 'right-end' | 'left' | 'left-start' | 'left-end'
  /** 选中数据传递出去的方法 */
  onClickActive?: (item: ModelsType) => void
}

const ModelTooltip = ({
  // item = activeItem,
  // options = models,
  models,
  isMobile,
  placement: placements = 'top-start',
  showBuiltIn,
  onClickActive,
}: ModelTooltipProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [clickObject, setClickObject] = useState<any>()

  const { refs, floatingStyles, context } = useFloating({
    placement: placements,
    open: isOpen,
    onOpenChange: (bool: boolean) => setIsOpen(bool),
    middleware: [
      offset(10),
      flip(),
      shift(),
    ],
  })

  const click = useClick(context)
  const role = useRole(context)
  const dismiss = useDismiss(context)

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    role,
    dismiss,
  ])

  /**
 * 处理模型激活事件的函数
 * @param model 激活的模型，属于 ModelsType 类型
 */
  const handleActive = (model: ModelsType) => {
    setIsOpen(false)
    setClickObject(model)
    // 如果 onClickActive 函数存在，则调用它，并传入当前模型作为参数
    onClickActive && onClickActive(model)
  }

  const options = useMemo(() => models?.filter(item => item.enable)?.map((item) => {
    const temp = {
      label: item.model_name_cn,
      value: item.model_name_en,
      icon: item.icon,
      isBuiltIn: item.is_built_in,
      isDefault: item.is_default,
    }

    return temp
  }), [models])

  // 非自由对话场景展示内置模型
  const builtInLlm = options?.find(item => item.isBuiltIn)

  useEffect(() => {
    if(showBuiltIn) {
      // 非自由对话场景选中内置模型
      const temp = options.find(item => item.isBuiltIn)

      temp && handleActive(temp)
    }
    else {
      // 自由对话场景选中默认模型
      const temp = options.find(item => item.isDefault)

      temp && handleActive(temp)
    }
  }, [showBuiltIn, options])

  if (!options?.length)
    return null

  if (showBuiltIn) {
    return builtInLlm
    ? <Tooltip popupContent="模型选择">
      <div className={
        `flex h-9 w-fit cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]
        ${isMobile && 'flex h-[48px] !w-[48px] items-center justify-center rounded-[24px] bg-[#fff] !p-[0px]'}
        `
      }>
        <Image src={builtInLlm?.icon || ''} width={isMobile ? 36 : 26} height={isMobile ? 36 : 26} alt='icon'></Image>
      </div>
    </Tooltip>
    : null
  }

  return (
      <div className={'inline-block'}>
          {clickObject && (
            <Tooltip popupContent="模型选择">
              <div>
                <div
                  ref={refs.setReference}
                  {...getReferenceProps()}
                  className={
                    `flex h-9 cursor-pointer items-center rounded-2xl bg-[#F1F2F3] p-1 text-sm text-[#434B5B]
                    ${isMobile && 'h-[48px] min-w-fit rounded-[24px] bg-[#fff] px-[10px]'}
                    `
                  }
                >
                  <Image src={clickObject?.icon} width={26} height={26} alt='icon'></Image>
                  {options.length > 1 && <RiArrowDownSLine className={`min-w-5 text-[#A3AFBB] ${isMobile && 'w-1'}`}></RiArrowDownSLine>}
                </div>
              </div>
            </Tooltip>
          )}

        {isOpen && options.length > 1 && (
          <FloatingPortal>
            <div
              ref={refs.setFloating}
              style={floatingStyles}
              {...getFloatingProps()}
              className="model-body z-50 min-w-44 border border-gray-200"
            >
              {options?.map((model, index) => (
                <div
                  key={index}
                  className={`model-item mb-2 flex cursor-pointer items-center rounded-lg px-2 py-1 ${model.label === clickObject?.label ? 'active' : ''}`}
                  onClick={() => handleActive(model)}>
                  {
                    model.icon && <Image className='mr-3 h-[30px] w-[30px]' src={model.icon} width={30} height={30} alt='icon'></Image>
                  }
                  <span className="model-item-text flex-1 whitespace-nowrap text-sm">{model.label}</span>
                </div>
              ))}
            </div>
          </FloatingPortal>
        )}
      </div>
  )
}

export default ModelTooltip
