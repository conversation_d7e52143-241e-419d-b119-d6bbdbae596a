import type {
  FC,
  ReactNode,
} from 'react'
import {
  memo,
  useCallback,
  useState,
} from 'react'
import type { ChatItem } from '../types'
import type { Theme } from '../embedded-chatbot/theme/theme-context'
// import { CssTransform } from '../embedded-chatbot/theme/utils'
import ContentSwitch from './content-switch'
import { useChatWithHistoryContext } from '../chat-with-history/context'
import { User } from '@/app/components/base/icons/src/public/avatar'
import { Markdown } from '@/app/components/base/markdown'
import { FileList } from '@/app/components/base/file-uploader'
import ActionButton from '../../action-button'
import { RiClipboardLine, RiEditLine } from '@remixicon/react'
import Toast from '../../toast'
import copy from 'copy-to-clipboard'
import { useTranslation } from 'react-i18next'
import cn from '@/utils/classnames'
import Textarea from 'react-textarea-autosize'
import Button from '../../button'
import { useChatContext } from './context'
import type { EmbedSource } from '@/models/share'

type QuestionProps = {
  item: ChatItem
  questionIcon?: ReactNode
  theme: Theme | null | undefined
  switchSibling?: (siblingMessageId: string) => void
  avatar?: string
  embedSource?: EmbedSource
  userName?: string
}

const backgroundColor = [['#7d67ff', '#99c2ff'], ['#5099ff', '#c9d8ff'], ['#7d67ff', '#f0e7ff'], ['#5099ff', '#e6e2ff'], ['#a3b9ff', '#99c2ff']]

const Question: FC<QuestionProps> = ({
  item,
  questionIcon,
  theme,
  switchSibling,
  avatar,
  embedSource,
  userName,
}) => {
  const { t } = useTranslation()

  const {
    content,
    message_files,
  } = item
  const { appData } = useChatWithHistoryContext()
  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const questionBgGradientFrom = chatPageConfigData?.questionBgGradientFrom || '#BFB3FF'
  const questionBgGradientTo = chatPageConfigData?.questionBgGradientTo || '#ACCEFF'

  // 随机获取背景色
  const randomIndex = localStorage.getItem('avatarBg') ?? Math.floor(Math.random() * 5)
  if (localStorage.getItem('avatarBg') === null)
    localStorage.setItem('avatarBg', String(randomIndex))

  const avatarBgColor = backgroundColor[randomIndex as any]

  const {
    onRegenerate,
  } = useChatContext()

  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(content)

  const handleEdit = useCallback(() => {
    setIsEditing(true)
    setEditedContent(content)
  }, [content])

  const handleResend = useCallback(() => {
    setIsEditing(false)
    onRegenerate?.(item, { message: editedContent, files: message_files })
  }, [editedContent, message_files, item, onRegenerate])

  const handleCancelEditing = useCallback(() => {
    setIsEditing(false)
    setEditedContent(content)
  }, [content])

  const handleSwitchSibling = useCallback((direction: 'prev' | 'next') => {
    if (direction === 'prev')
      item.prevSibling && switchSibling?.(item.prevSibling)
    else
      item.nextSibling && switchSibling?.(item.nextSibling)
  }, [switchSibling, item.prevSibling, item.nextSibling])

  return (
    <div className='mb-2 flex justify-end pl-14 last:mb-0'>
      <div className={cn('group relative mr-4 flex max-w-full items-start', isEditing && 'flex-1')}>
        <div className={cn('mr-2 gap-1', isEditing ? 'hidden' : 'flex')}>
          <div className="
            absolutegap-0.5 hidden rounded-[10px] border-[0.5px] border-components-actionbar-border
            bg-components-actionbar-bg p-0.5 shadow-md backdrop-blur-sm group-hover:flex
          ">
            <ActionButton onClick={() => {
              copy(content)
              Toast.notify({ type: 'success', message: t('common.actionMsg.copySuccessfully') })
            }}>
              <RiClipboardLine className='h-4 w-4' />
            </ActionButton>
            <ActionButton onClick={handleEdit}>
              <RiEditLine className='h-4 w-4' />
            </ActionButton>
          </div>
        </div>
        <div
          className={`w-full rounded-2xl bg-[#D1E9FF]/50 px-4 py-3 text-sm text-gray-900 ${embedSource && 'rounded-[12px] rounded-br-[0px]'}`}
          style={{
            // ...(theme?.chatBubbleColorStyle
            //   ? CssTransform(theme.chatBubbleColorStyle)
            //   : {}),
            backgroundImage: embedSource
              ? `linear-gradient(270deg, ${questionBgGradientFrom} 0%, ${questionBgGradientTo} 100%)`
              : '',
          }}
        >
          {
            !!message_files?.length && (
              <FileList
                className='mb-2'
                files={message_files}
                showDeleteAction={false}
                showDownloadAction={true}
              />
            )
          }
          { !isEditing
            ? <Markdown content={content} />
            : <div className="
                flex flex-col gap-2 rounded-xl
                border border-components-chat-input-border bg-components-panel-bg-blur p-[9px] shadow-md
              ">
              <div className="max-h-[158px] overflow-y-auto overflow-x-hidden">
                <Textarea
                  className={cn(
                    'body-lg-regular w-full p-1 leading-6 text-text-tertiary outline-none',
                  )}
                  autoFocus
                  minRows={1}
                  value={editedContent}
                  onChange={e => setEditedContent(e.target.value)}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant='ghost' onClick={handleCancelEditing}>{t('common.operation.cancel')}</Button>
                <Button variant='primary' onClick={handleResend}>{t('common.chat.resend')}</Button>
              </div>
            </div> }
          { !isEditing && <ContentSwitch
            count={item.siblingCount}
            currentIndex={item.siblingIndex}
            prevDisabled={!item.prevSibling}
            nextDisabled={!item.nextSibling}
            switchSibling={handleSwitchSibling}
          />}
        </div>
        <div className='mt-1 h-[18px]' />
      </div>
      <div className='h-10 w-10 shrink-0'>
        {
          questionIcon || (
            <div className='h-full w-full rounded-full border-[0.5px] border-black/5'>
              <div className="w-full h-full rounded-full border-[0.5px] border-black/5">
                {avatar
                  ? (
                    <img src={avatar} className="w-full h-full rounded-full" />
                  )
                  : (embedSource && userName && userName.length >= 2)
                    ? (
                      <div
                        className={'flex items-center justify-center w-full h-full rounded-full text-[14px] text-white'}
                        style={{ backgroundImage: `linear-gradient(135deg, ${avatarBgColor[0]},${avatarBgColor[1]})` }}
                      >
                        {userName.slice(-2)}
                      </div>
                    )
                    : (
                      <User className="w-full h-full" />
                    )}
              </div>
            </div>
          )
        }
      </div>
    </div>
  )
}

export default memo(Question)
