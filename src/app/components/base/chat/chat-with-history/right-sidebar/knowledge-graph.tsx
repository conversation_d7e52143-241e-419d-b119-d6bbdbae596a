import { useEffect, useRef, useState } from 'react'
import { Graph } from '@antv/g6'
import './knowledge-graph.css'

const data = {
  entities: [
    {
      type: 'entity',
      entityType: 'Person',
      name: '周凯凯',
      observations: [],
    },
    {
      type: 'entity',
      entityType: 'Person',
      name: '李大大',
      observations: [],
    },
    {
      type: 'entity',
      entityType: 'Location',
      name: '深圳',
      observations: [],
    },
    {
      type: 'entity',
      entityType: 'Location',
      name: '北京',
      observations: [],
    },
    {
      type: 'entity',
      entityType: '地名',
      name: '深圳湾',
      observations: [],
    },
    {
      type: 'entity',
      entityType: '社交活动',
      name: '与朋友吃饭',
      observations: ['在深圳湾', '非常开心'],
    },
    {
      type: 'entity',
      entityType: '客户沟通',
      name: '智谱客户沟通',
      observations: ['涉及智能家居方案'],
    },
    {
      type: 'entity',
      entityType: 'Event',
      name: '与思必驰团队沟通',
      observations: [
        '讨论智能家居语音控制方案',
        '发生在前天',
      ],
    },
    {
      type: 'entity',
      entityType: 'Company',
      name: '思必驰',
      observations: [],
    },
    {
      type: 'entity',
      entityType: 'Event',
      name: '沟通华为模型平台',
      observations: ['时间在今天下午', '与华为沟通'],
    },
    {
      type: 'entity',
      entityType: 'meeting',
      name: '与成本管理部的需求沟通',
      observations: ['时间：明天'],
    },
  ],
  relations: [
    {
      type: 'relation',
      from: '周凯凯',
      relationType: '好朋友',
      to: '李大大',
    },
    {
      type: 'relation',
      from: '李大大',
      relationType: 'lives in',
      to: '北京',
    },
  ],
}

const KnowledgeGraph = () => {
  const graphRef = useRef<HTMLDivElement>(null)
  const graph = useRef<any>()
  const [isError, setIsError] = useState(false)

  const initGraph = async () => {
    try {
      graph.current = new Graph({
        container: graphRef.current!,
        width: 400,
        autoFit: 'view',
        padding: [20, 40, 20, 40],
        background: '#fff',
        zoomRange: [0.1, 1.5],
        layout: {
          type: 'force-atlas2',
          kr: 120,
          center: [250, 250],
          preventOverlap: true,
        },
        data: {
          nodes: data.entities.map(({ name, observations }: any) => ({
            id: name,
            label: name,
            data: { observations },
            style: {
              label: true, // 是否显示节点标签
              labelText: name, // 标签文字内容
            },
          })),
          edges: data.relations.map(({ from, to, relationType }: any) => ({
            id: `${from}-${to}`,
            source: from,
            target: to,
            label: relationType,
            style: {
              labelText: relationType,
              labelBackground: true,
              labelBackgroundFill: '#fff',
              endArrow: true,
            },
          })),
        },
        behaviors: ['drag-canvas', 'zoom-canvas'],
        plugins: [
          {
            type: 'tooltip',
            getContent: (e, items) => {
              const observations = items[0].data?.observations

              return observations?.join('、')
            },
          },
          {
            type: 'fullscreen',
            key: 'fullscreen',
          },
          function () {
            return {
              type: 'toolbar',
              position: 'right-top',
              onClick: (key: string) => {
                const fullscreenPlugin = this.getPluginInstance('fullscreen')

                switch(key) {
                  case 'zoom-in':
                    graph.current?.zoomTo(graph.current.getZoom() + 0.1)
                    break
                  case 'zoom-out':
                    graph.current?.zoomTo(graph.current.getZoom() - 0.1)
                    break
                  case 'auto-fit':
                    graph.current?.fitView()
                    break
                  case 'request-fullscreen':
                    fullscreenPlugin?.request()
                    break
                  case 'exit-fullscreen':
                    fullscreenPlugin?.exit()
                    break
                }
              },
              getItems: () => {
                return [
                  { id: 'zoom-in', value: 'zoom-in', style: { display: 'flex' } },
                  { id: 'zoom-out', value: 'zoom-out', style: { display: 'flex' } },
                  { id: 'auto-fit', value: 'auto-fit', style: { display: 'flex' } },
                  { id: 'request-fullscreen', value: 'request-fullscreen', style: { display: 'flex' } },
                  { id: 'exit-fullscreen', value: 'exit-fullscreen', style: { display: 'flex' } },
                ]
              },
            }
          },
        ],
      })

      graph.current?.render()
    }
    catch(err) {
      setIsError(true)
    }
  }

  useEffect(() => {
    if(graphRef.current) {
      const observer = new ResizeObserver(async () => {
        graph.current.resize()
        graph.current?.fitView()
      })

      observer.observe(graphRef.current)
    }
  }, [])

  useEffect(() => {
    initGraph()

    return() => graph.current?.clear()
  }, [])

  if(isError) return <div className='flex h-full items-center justify-center bg-white text-[14px]'>数据异常,请重试</div>

  return <div className='h-full' ref={graphRef} />
}

export default KnowledgeGraph
