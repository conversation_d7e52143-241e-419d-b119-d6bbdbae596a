import Image from '@/app/components/base/image'
import cn from 'classnames'
import { type ReactNode, useEffect, useRef, useState } from 'react'
import IconRight from '@/assets/right.svg'
import IconWebSearch from '@/assets/web-search.svg'
import IconArrowRight from '@/assets/arrow-right.svg'
import IconClose from '@/assets/close.svg'
import { useChatWithHistoryContext } from '../context'
import positions from 'positions'
import ToolDetail from '../../chat/answer/tool-detail'

export const RightSidebarTrigger = ({ process, status, references, children, isMobile }: { process: string, status: string, references: Record<string, any>[], children: ReactNode, isMobile?: boolean }) => {
  const { rightSideInfo, setRightSideInfo } = useChatWithHistoryContext()
  const [popCss, setPopCss] = useState<null | Record<string, any>>(null)
  const timer = useRef<number>(-1)
  const popIndex = useRef(-1)
  const isEnter = useRef(false)

  const handleMouseOver = (e: MouseEvent) => {
    const target = e.target as HTMLElement

    if(target.dataset?.reference && isEnter.current) {
      clearTimeout(timer.current)
      const css = positions(document, 'top left', target, 'bottom left')
      popIndex.current = target.dataset?.reference ? (Number(target.dataset.reference) - 1) : -1
      setPopCss(css)
    }
  }

  const handleMouseLeave = (e: MouseEvent) => {
    const target = e.target as HTMLElement

    if(target.dataset?.reference) {
      timer.current = window.setTimeout(() => {
        setPopCss(null)
        popIndex.current = -1
      }, 50)
    }
  }

  const handleClick = (e) => {
    const target = e.target as HTMLElement

    if(target.dataset?.reference) {
      e.preventDefault()

      const index = Number(target.dataset?.reference) - 1
      const temp = references[index]

      if(temp) {
        const link = document.createElement('a')
        link.href = temp.url
        link.target = '_blank'
        link.click()
      }
    }
  }

  useEffect(() => {
    if(isMobile) {
      document.documentElement.addEventListener('touchstart', handleClick, { passive: false })
    }
    else {
      document.documentElement.addEventListener('mouseover', handleMouseOver)
      document.documentElement.addEventListener('mouseout', handleMouseLeave)
    }

    return () => {
      document.documentElement.removeEventListener('touchstart', handleClick)
      document.documentElement.removeEventListener('mouseover', handleMouseOver)
      document.documentElement.removeEventListener('mouseout', handleMouseLeave)
    }
  }, [isMobile])

  const handleToggle = () => {
    setRightSideInfo?.(rightSideInfo === references ? null : references)
  }

  const handlePopMouseEnter = () => {
    if (timer.current) {
      clearTimeout(timer.current)
      timer.current = -1
    }
  }

  const handlePopMouseLeave = () => {
    setPopCss(null)
  }

  const renderReferencePop = ({ style }: { style: any }) => {
    const temp = references[popIndex.current]

    if(!temp) return null

    return (
      <a className='z-[999] block w-[300px] rounded-[8px] bg-white p-[10px] shadow-lg' href={temp?.url} target='_blank' style={style} onMouseEnter={handlePopMouseEnter} onMouseLeave={handlePopMouseLeave}>
        <h2 className={styles['line-clamp-2']}>{temp?.title}</h2>
        <div className='mt-[10px] flex justify-between text-[12px]'>
          <span>{temp?.site_name}</span>
          <Image
            className="ml-[4px] mr-[2px]"
            src={IconArrowRight}
            alt=""
            width={16}
            height={16}
          ></Image>
        </div>
      </a>
    )
  }

  return (
    <div className='mb-[20px]' onMouseEnter={e => isEnter.current = true} onMouseLeave={() => isEnter.current = false}>
      {popCss && renderReferencePop({ style: { position: 'fixed', top: `${Math.floor(popCss?.top)}px`, left: `${popCss?.left}px` } })}
      <div className="mb-[10px] flex items-center">
        {status === 'start' ? <>
          <div className="flex items-center text-[#70757f]">
            <Image
              className="mr-[2px]"
              src={IconWebSearch}
              alt=""
              width={18}
              height={18}
            ></Image>
          </div>
          <span className="ml-[4px] text-[#959696]">{process || '正在搜索...'}</span>
        </> : <div className='flex cursor-pointer items-center rounded-[4px] py-[2px] pl-[2px] pr-[10px] hover:bg-[#edeeee]' onClick={handleToggle}>
            <Image
              className="mr-[2px]"
              src={IconWebSearch}
              alt=""
              width={18}
              height={18}
            ></Image>
            <span className="ml-[4px] text-[#959696]">{process}</span>
            <Image
              className="ml-[4px]"
              src={IconRight}
              alt=""
              width={16}
              height={16}
            ></Image>
          </div>}
      </div>
      <div className='my-2 space-y-2'>
      <ToolDetail
          payload={{
            name: 'test',
            label: 'test',
            input: JSON.stringify({
              query: '\u4E0B\u5468\u4E09',
              user_id: '20210514093115658-1B59-6A36278EC',
            }),
            output: '{\n  "entities": [\n    {\n      "type": "entity",\n      "entityType": "\u4F1A\u8BAE",\n      "name": "\u9879\u76EE\u4E0A\u7EBF\u4F1A\u8BAE",\n      "observations": [\n        "\u7528\u6237\u5C06\u5728\u4E0B\u5468\u4E09\u53C2\u52A0\u6B64\u4F1A\u8BAE"\n      ]\n    }\n  ],\n  "relations": []\n}',
            isFinished: true,
          }}
        />
         <ToolDetail
          payload={{
            name: 'test',
            label: 'test',
            input: JSON.stringify({
              query: '\u4E0B\u5468\u4E09',
              user_id: '20210514093115658-1B59-6A36278EC',
            }),
            output: '{\n  "entities": [\n    {\n      "type": "entity",\n      "entityType": "\u4F1A\u8BAE",\n      "name": "\u9879\u76EE\u4E0A\u7EBF\u4F1A\u8BAE",\n      "observations": [\n        "\u7528\u6237\u5C06\u5728\u4E0B\u5468\u4E09\u53C2\u52A0\u6B64\u4F1A\u8BAE"\n      ]\n    }\n  ],\n  "relations": []\n}',
            isFinished: true,
          }}
        />
      </div>
      {children}
    </div>
  )
}

const WebSearchContent = ({ isMobile }: { isMobile: boolean }) => {
  const { rightSideInfo, setRightSideInfo } = useChatWithHistoryContext()

  const handleClick = () => {
    setRightSideInfo?.(null)
  }

  return (
    <div className={cn('flex h-full w-[400px] flex-col bg-[#fafafd]', isMobile && '!w-full rounded-t-[16px]')}>
    <div className={cn('flex items-center justify-between border-b border-[#e8e8e8] px-[16px] py-[20px] text-[16px]', isMobile && 'border-none !py-[10px]')}>
      网页搜索
      <Image
        className="cursor-pointer"
        src={IconClose}
        alt=""
        width={24}
        height={24}
        onClick={handleClick}
      />
    </div>
    <ul className="flex-1 overflow-auto p-[8px]">
      {rightSideInfo?.map((item: any) => (
        <li className="mb-[10px] cursor-pointer rounded-[10px] px-[12px] py-[8px] hover:bg-[#f5f5f5]">
          <a className='flex' href={item.url} target="_blank" rel="noreferrer">
            {item?.cover_image && (
              <img
                className='mr-[6px] h-[100px] w-[100px] rounded-[8px] object-cover'
                src={item?.cover_image?.url}
                alt="图片"
              />
            )}
            <div className='flex flex-col justify-between'>
              <h2
                className={cn(
                  'line-clamp-2 text-[16px] leading-[24px]',
                )}
              >
              {item.title}
              </h2>
              <p
                className={cn(
                  'line-clamp-2 text-[14px] leading-[22px] text-[#70757f]',
                )}
              >
                {item.summary}
              </p>
              <div>
                <span className='text-[14px] text-[#70757f]'>{item.site_name}</span>
              </div>
            </div>
          </a>
        </li>
      ))}
    </ul>
  </div>
  )
}

const RightSidebar = ({ visible, isMobile }: { visible: boolean, isMobile: boolean }) => {
  // if(!visible) return null

  return (
    isMobile ? <div className={cn('absolute left-[0px] top-[0px] z-[999] flex h-full w-full items-end bg-[#0009] transition-opacity duration-100 ease-linear', !visible && 'pointer-events-none opacity-0 delay-200')}>
      <div className={cn('w-full transition-all duration-300 ease-linear', visible ? 'h-[90%] opacity-100' : 'h-[0px] opacity-0')}>
        <WebSearchContent isMobile={isMobile}/>
      </div>
    </div>
    : <div className={cn('z-10 border-l border-[#e8e8e8] transition-all duration-300 ease-out', visible ? 'w-[400px] opacity-100' : 'w-[0px] opacity-0')}>
      {/* <KnowledgeGraph/> */}
      <WebSearchContent isMobile={isMobile}/>
    </div>
  )
}

export default RightSidebar
