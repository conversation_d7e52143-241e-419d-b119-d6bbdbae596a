.bg {
    background-image: url('~@/assets/lark-chat-top.jpeg');
    background-position: top;
    background-size: contain;
    background-repeat: no-repeat;
}

.mobileNavTitle {
    background: linear-gradient(to bottom, #562DF1, #2381FF); 
    -webkit-background-clip: text; 
    color: transparent; 
}

.navBarBg::after {
    content: '';
    height: 100vh;
    background: linear-gradient(var(--start-color), var(--end-color) 20%);
    position: absolute;
    width: 100vw;
    z-index: -1;
    top: 0;
    left: 0;
}
.errorPng {
    background-image: url('~@/assets/404.png');
    background-position: top;
    background-size: contain;
    background-repeat: no-repeat;
}