import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  RiMenuLine,
} from '@remixicon/react'
import { useChatWithHistoryContext } from './context'
import Operation from './header/operation'
import Sidebar from './sidebar'
import MobileOperationDropdown from './header/mobile-operation-dropdown'
import styles from './index.module.css'
import AppIcon from '@/app/components/base/app-icon'
import ActionButton from '@/app/components/base/action-button'
import { Message3Fill } from '@/app/components/base/icons/src/public/other'
import InputsFormContent from '@/app/components/base/chat/chat-with-history/inputs-form/content'
import Confirm from '@/app/components/base/confirm'
import RenameModal from '@/app/components/base/chat/chat-with-history/sidebar/rename-modal'
import type { ConversationItem } from '@/models/share'

const LarkNewChatIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className={className}>
    <path d="M10,13.3333333 L10,10 M10,10 L10,6.66666667 M10,10 L6.66666667,10 M10,10 L13.3333333,10 M10.0001111,20 C8.18333333,20 6.47971111,19.5154444 5.01138889,18.6686667 C4.86633333,18.585 4.79371111,18.5431111 4.72547778,18.5243333 C4.66197778,18.5067778 4.60527778,18.5007778 4.53954444,18.5053333 C4.46947778,18.5101111 4.397,18.5342222 4.25287778,18.5823333 L1.68674444,19.4376667 L1.68472222,19.4385556 C1.14324444,19.6191111 0.872,19.7095556 0.691733333,19.6452222 C0.534666667,19.5892222 0.410877778,19.4652222 0.354866667,19.3082222 C0.290633333,19.128 0.380755556,18.8576667 0.560988889,18.317 L0.562066667,18.3136667 L1.41631111,15.7508889 L1.41834444,15.7455556 C1.46595556,15.6026667 1.49006667,15.5303333 1.49483333,15.4606667 C1.49933333,15.3948889 1.49334444,15.3377778 1.47578889,15.2743333 C1.45717778,15.207 1.41608889,15.1356667 1.3346,14.9944444 L1.33132222,14.9887778 C0.484488889,13.5204444 0,11.8167778 0,10 C0,4.47715556 4.47715556,0 10,0 C15.5228889,0 20,4.47715556 20,10 C20,15.5228889 15.523,20 10.0001111,20 Z" transform="translate(2 2)" stroke="#434B5B" strokeWidth="2" fill="none" fillRule="evenodd" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
)

const HeaderInMobile = () => {
  const {
    appData,
    currentConversationId,
    currentConversationItem,
    pinnedConversationList,
    handleNewConversation,
    handlePinConversation,
    handleUnpinConversation,
    handleDeleteConversation,
    handleRenameConversation,
    conversationRenaming,
    embedSource,
  } = useChatWithHistoryContext()
  const { t } = useTranslation()
  const isPin = pinnedConversationList.some(item => item.id === currentConversationId)
  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)
  const [showRename, setShowRename] = useState<ConversationItem | null>(null)
  const handleOperate = useCallback((type: string) => {
    if (type === 'pin')
      handlePinConversation(currentConversationId)

    if (type === 'unpin')
      handleUnpinConversation(currentConversationId)

    if (type === 'delete')
      setShowConfirm(currentConversationItem as any)

    if (type === 'rename')
      setShowRename(currentConversationItem as any)
  }, [currentConversationId, currentConversationItem, handlePinConversation, handleUnpinConversation])
  const handleCancelConfirm = useCallback(() => {
    setShowConfirm(null)
  }, [])
  const handleDelete = useCallback(() => {
    if (showConfirm)
      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })
  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])
  const handleCancelRename = useCallback(() => {
    setShowRename(null)
  }, [])
  const handleRename = useCallback((newName: string) => {
    if (showRename)
      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })
  }, [showRename, handleRenameConversation, handleCancelRename])
  const [showSidebar, setShowSidebar] = useState(false)
  const [showChatSettings, setShowChatSettings] = useState(false)

  return (
    <>
      <div className={`flex shrink-0 items-center gap-1 bg-mask-top2bottom-gray-50-to-transparent px-2 py-3 ${embedSource && 'h-[48px] border-none pt-[8px]'}`}>
        <ActionButton size='l' className='shrink-0' onClick={() => setShowSidebar(true)}>
          <RiMenuLine className='h-[18px] w-[18px]' />
        </ActionButton>
        <div className='flex grow items-center justify-center'>
          {!currentConversationId && (
            <>
              {!embedSource && (
                <AppIcon
                  className='mr-2'
                  size='tiny'
                  icon={appData?.site.icon}
                  iconType={appData?.site.icon_type}
                  imageUrl={appData?.site.icon_url}
                  background={appData?.site.icon_background}
                />
              )}
              <div className={`system-md-semibold truncate text-text-secondary ${embedSource && 'text-xl !font-medium'} ${embedSource && styles.mobileNavTitle}`}>
                {appData?.site.title}
              </div>
            </>
          )}
          {currentConversationId && (
            <Operation
              title={currentConversationItem?.name || ''}
              isPinned={!!isPin}
              togglePin={() => handleOperate(isPin ? 'unpin' : 'pin')}
              isShowDelete
              isShowRenameConversation
              onRenameConversation={() => handleOperate('rename')}
              onDelete={() => handleOperate('delete')}
            />
          )}
        </div>
        {embedSource
          ? (
            <div
              className='flex h-8 w-8 shrink-0 items-center justify-center rounded-lg'
              onClick={handleNewConversation}
            >
              <LarkNewChatIcon className="w-[22px]" />
            </div>
          ) : (
            <MobileOperationDropdown
            handleResetChat={handleNewConversation}
            handleViewChatSettings={() => setShowChatSettings(true)}
          />
        )}
      </div>
      {showSidebar && (
        <div className='fixed inset-0 z-50 flex bg-background-overlay p-1'
          style={{ backgroundColor: embedSource ? 'rgba(0,0,0,.7)' : '' }}
          onClick={() => setShowSidebar(false)}
        >
          <div className='flex h-full w-[calc(100vw_-_40px)] rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>
            <Sidebar />
          </div>
        </div>
      )}
      {showChatSettings && (
        <div className='fixed inset-0 z-50 flex justify-end bg-background-overlay p-1'
          onClick={() => setShowChatSettings(false)}
        >
          <div className='flex h-full w-[calc(100vw_-_40px)] flex-col rounded-xl bg-components-panel-bg shadow-lg backdrop-blur-sm' onClick={e => e.stopPropagation()}>
            <div className='flex items-center gap-3 rounded-t-2xl border-b border-divider-subtle px-4 py-3'>
              <Message3Fill className='h-6 w-6 shrink-0' />
              <div className='system-xl-semibold grow text-text-secondary'>{t('share.chat.chatSettingsTitle')}</div>
            </div>
            <div className='p-4'>
              <InputsFormContent />
            </div>
          </div>
        </div>
      )}
      {!!showConfirm && (
        <Confirm
          title={t('share.chat.deleteConversation.title')}
          content={t('share.chat.deleteConversation.content') || ''}
          isShow
          onCancel={handleCancelConfirm}
          onConfirm={handleDelete}
        />
      )}
      {showRename && (
        <RenameModal
          isShow
          onClose={handleCancelRename}
          saveLoading={conversationRenaming}
          name={showRename?.name || ''}
          onSave={handleRename}
        />
      )}
    </>
  )
}

export default HeaderInMobile
