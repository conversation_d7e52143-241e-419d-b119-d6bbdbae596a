import type { FC } from 'react'
import {
  useEffect,
  useState,
} from 'react'
import { useAsyncEffect } from 'ahooks'
import LoadScript from 'react-load-script'
import { useThemeContext } from '../embedded-chatbot/theme/theme-context'
import {
  ChatWithHistoryContext,
  useChatWithHistoryContext,
} from './context'
import { useChatWithHistory } from './hooks'
import Sidebar from './sidebar'
import Header from './header'
import HeaderInMobile from './header-in-mobile'
import ChatWrapper from './chat-wrapper'
import styles from './index.module.css'
import type { InstalledApp } from '@/models/explore'
import Loading from '@/app/components/base/loading'
import useBreakpoints, { MediaType } from '@/hooks/use-breakpoints'
import { checkOrSetAccessToken } from '@/app/components/share/utils'
import AppUnavailable from '@/app/components/base/app-unavailable'
import cn from '@/utils/classnames'
import type { EmbedSource } from '@/models/share'
import { getQueryParam } from '@/utils'
import useCheckCscecVersion from '@/hooks/use-check-cscec-version'
// import getAuth from '@/hooks/use-platform-auth'
import RightSidebar from './right-sidebar'
import { EMBED_SOURCE_TYPE, MAX_RETRY_COUNT } from '@/config'

// 应用嵌入时不展示404
const isEmbed = getQueryParam()?.get('state')

type ChatWithHistoryProps = {
  className?: string
}

const ChatWithHistory: FC<ChatWithHistoryProps> = ({
  className,
}) => {
  const {
    appInfoError,
    appData,
    appInfoLoading,
    appChatListDataLoading,
    chatShouldReloadKey,
    isMobile,
    themeBuilder,
    sidebarCollapseState,
    refreshRenderKey,
    isFold,
    embedSource,
    rightSideInfo,
  } = useChatWithHistoryContext()
  const isSidebarCollapsed = sidebarCollapseState
  const customConfig = appData?.custom_config
  const site = appData?.site

  const [showSidePanel, setShowSidePanel] = useState(false)

  useEffect(() => {
    themeBuilder?.buildTheme(site?.chat_color_theme, site?.chat_color_theme_inverted)
    if (site) {
      if (customConfig)
        document.title = `${site.title}`
      else
        document.title = `${site.title}`
    }
  }, [site, customConfig, themeBuilder])

  if (appInfoLoading || (isEmbed && appInfoError)) {
    return (
      <Loading />
    )
  }

  if (appInfoError && !isEmbed) {
    return (
      <AppUnavailable />
    )
  }

  return (
    <div className={cn(
      'flex h-full bg-background-default-burn',
      isMobile && 'flex-col',
      className,
      embedSource && isMobile && '!bg-[#f5f6f8]',
      embedSource && isMobile && styles.bg,
    )}>
      {(!isMobile && !isFold) && (
        <div className={cn(
          'flex w-[236px] flex-col pr-0 transition-all duration-200 ease-in-out',
          isSidebarCollapsed && 'w-0 overflow-hidden !p-0',
          embedSource && !isMobile && '!bg-white',
        )}>
          <Sidebar />
        </div>
      )}
      {isMobile && (
        <HeaderInMobile />
      )}
      <div className={cn('relative grow p-2', embedSource && 'p-0', isMobile && 'h-[calc(100%_-_56px)] p-0')}>
        {isSidebarCollapsed && (
          <div
            className={cn(
              'absolute top-0 z-20 flex h-full w-[256px] flex-col p-2 transition-all duration-500 ease-in-out',
              showSidePanel ? 'left-0' : 'left-[-248px]',
            )}
            onMouseEnter={() => setShowSidePanel(true)}
            onMouseLeave={() => setShowSidePanel(false)}
          >
            <Sidebar isPanel />
          </div>
        )}
        <div className={cn('flex h-full flex-col overflow-hidden border-[0,5px] border-components-panel-border-subtle bg-chatbot-bg', isMobile ? 'rounded-t-2xl' : 'rounded-2xl', embedSource && 'rounded-none')}>
          {!isMobile && !embedSource && <Header />}
          {appChatListDataLoading && (
            <Loading />
          )}
          {!appChatListDataLoading && (
            <ChatWrapper key={chatShouldReloadKey || refreshRenderKey} />
          )}
        </div>
      </div>
      <RightSidebar isMobile={isMobile} visible={Boolean(rightSideInfo)}/>
      <LoadScript url="/aippt-iframe-sdk.js"/>
    </div>
  )
}

export type ChatWithHistoryWrapProps = {
  installedAppInfo?: InstalledApp
  className?: string
  larkInfo?: any
  embedSource?: EmbedSource
}
const ChatWithHistoryWrap: FC<ChatWithHistoryWrapProps> = ({
  installedAppInfo,
  larkInfo,
  embedSource,
  className,
}) => {
  const media = useBreakpoints()
  const isMobile = media === MediaType.mobile
  const themeBuilder = useThemeContext()
  const [isFold, setIsFold] = useState<boolean>(true)

  const {
    appInfoError,
    appInfoLoading,
    appData,
    appParams,
    appMeta,
    appChatListDataLoading,
    currentConversationId,
    currentConversationItem,
    appPrevChatTree,
    pinnedConversationList,
    conversationList,
    newConversationInputs,
    newConversationInputsRef,
    handleNewConversationInputsChange,
    inputsForms,
    handleNewConversation,
    handleStartChat,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    handleDeleteConversation,
    conversationRenaming,
    handleRenameConversation,
    handleNewConversationCompleted,
    chatShouldReloadKey,
    isInstalledApp,
    appId,
    handleFeedback,
    currentChatInstanceRef,
    sidebarCollapseState,
    handleSidebarCollapse,
    clearChatList,
    setClearChatList,
    isResponding,
    setIsResponding,
    currentConversationInputs,
    setCurrentConversationInputs,
    refreshRenderKey,
    handleClearAllConversations,
    rightSideInfo,
    setRightSideInfo,
  } = useChatWithHistory(installedAppInfo, embedSource)

  useEffect(() => {
    setIsFold(!!embedSource)
  }, [embedSource])

  return (
    <ChatWithHistoryContext.Provider value={{
      appInfoError,
      appInfoLoading,
      appData,
      appParams,
      appMeta,
      appChatListDataLoading,
      currentConversationId,
      currentConversationItem,
      appPrevChatTree,
      pinnedConversationList,
      conversationList,
      newConversationInputs,
      newConversationInputsRef,
      handleNewConversationInputsChange,
      inputsForms,
      handleNewConversation,
      handleStartChat,
      handleChangeConversation,
      handlePinConversation,
      handleUnpinConversation,
      handleDeleteConversation,
      conversationRenaming,
      handleRenameConversation,
      handleNewConversationCompleted,
      chatShouldReloadKey,
      isMobile,
      isInstalledApp,
      appId,
      handleFeedback,
      currentChatInstanceRef,
      themeBuilder,
      sidebarCollapseState,
      handleSidebarCollapse,
      clearChatList,
      setClearChatList,
      isResponding,
      setIsResponding,
      currentConversationInputs,
      setCurrentConversationInputs,
      refreshRenderKey,
      embedSource,
      isFold,
      setIsFold,
      larkInfo,
      handleClearAllConversations,
      rightSideInfo,
      setRightSideInfo,
    }}>
      <ChatWithHistory className={className} />
    </ChatWithHistoryContext.Provider>
  )
}

const ChatWithHistoryWrapWithCheckToken: FC<ChatWithHistoryWrapProps> = ({
  installedAppInfo,
  className,
}) => {
  const [initialized, setInitialized] = useState(false)
  const [appUnavailable, setAppUnavailable] = useState<boolean>(false)
  const [isUnknownReason, setIsUnknownReason] = useState<boolean>(false)
  const [larkInfo, setLarkInfo] = useState({})
  const [embedSource, setEmbedSource] = useState<EmbedSource>('')
  const [showUpgrade, setShowUpgrade] = useState(false)
  const [errorStatus, setErrorStatus] = useState(false)
  const { needUpgrade } = useCheckCscecVersion()
  // const { needPlatformAuth } = getAuth()

  // useAsyncEffect(async () => {
  //   // 企业微信版本检测
  //   if (needUpgrade()) {
  //     setShowUpgrade(true)
  //     return
  //   }

  //   if(isEmbed && isEmbed !== EMBED_SOURCE_TYPE.TOKEN) {
  //     // oAuth2.0认证
  //     const auth = await needPlatformAuth()
  //     if (auth && typeof auth === 'object') {
  //       setErrorStatus(true)
  //       setInitialized(true)
  //       return
  //     }
  //     else if (auth && typeof auth === 'boolean') {
  //       console.log(auth)
  //       return
  //     }
  //   }

  //   if (!initialized) {
  //     if (!installedAppInfo) {
  //       try {
  //         const res: any = await checkOrSetAccessToken()
  //         if (res) {
  //           setLarkInfo(res?.userInfo)
  //           setEmbedSource(res?.embedApp)
  //         }
  //         else {
  //           // 嵌入式获取token后会重定向，所以跳过设置setInitialized
  //           if(isEmbed) {
  //             // setEmbedSource(process.env.NEXT_PUBLIC_SKIP_AUTH?.toLowerCase() ? EMBED_SOURCE_TYPE.FS : '' as any)
  //             return
  //           }
  //         }
  //       }
  //       catch (e: any) {
  //         const tryCount = Number(sessionStorage.getItem('retry_count'))

  //         if(isEmbed === EMBED_SOURCE_TYPE.TOKEN) {
  //           setErrorStatus(true)
  //           setInitialized(true)
  //           sessionStorage.removeItem('retry_count')
  //           return
  //         }

  //         if(tryCount < MAX_RETRY_COUNT) {
  //           sessionStorage.setItem('retry_count', String(tryCount + 1))
  //           return
  //         }
  //         else {
  //           setErrorStatus(true)
  //           sessionStorage.removeItem('retry_count')
  //         }

  //         if (e.status === 404) {
  //           setAppUnavailable(true)
  //         }
  //         else if (e.status === 500 || e.status === 401) {
  //           setErrorStatus(true)
  //         }
  //         else {
  //           setIsUnknownReason(true)
  //           setAppUnavailable(true)
  //         }
  //       }
  //     }
  //     setInitialized(true)
  //   }
  // }, [])

  if (showUpgrade) {
    return (
      <div className="mt-[100px] flex h-full w-full items-start justify-center px-[20px] text-[20px]">
        <div>
          <p>为了更好地体验AI办公助理应用，请您前往【中建通官网】，升级中建通至最新版本，<a className='text-[#1e86ff]' href="https://portal.cscec.com" target="_blank">下载地址</a>。</p>
        </div>
      </div>
    )
  }

  if (!initialized)
    return null

  if (appUnavailable && !isEmbed)
    return <AppUnavailable isUnknownReason={isUnknownReason} />

  if (errorStatus) {
    return (
      <div className="flex h-full w-full flex-col items-center justify-center">
        <div className={`mb-3 h-[300px] w-[300px] ${styles.errorPng}`}></div>
        <div className='text-[16px] text-[#666]'>
          授权好像有点问题，检查下啦~~<br></br>
          确认没问题的话，你就
          <span className='cursor-pointer text-[#1e86ff]' onClick={() => {
            globalThis.location.reload()
          }}> 刷新 </span>
          试试呗
        </div>
      </div>
    )
  }

  return (
    <ChatWithHistoryWrap
      installedAppInfo={installedAppInfo}
      larkInfo={larkInfo}
      embedSource={embedSource}
      className={className}
    />
  )
}

export default ChatWithHistoryWrapWithCheckToken
