import type { ChangeEvent } from 'react'
import {
  useCallback,
  useState,
} from 'react'
import { useTranslation } from 'react-i18next'
import {
  RiEditBoxLine,
  RiExpandRightLine,
  RiLayoutLeft2Line,
} from '@remixicon/react'
import { throttle } from 'lodash-es'
import { useChatWithHistoryContext } from '../context'
import BtnFold from '../btn-fold'
import AppIcon from '@/app/components/base/app-icon'
import ActionButton from '@/app/components/base/action-button'
import Button from '@/app/components/base/button'
import List from '@/app/components/base/chat/chat-with-history/sidebar/list'
import MenuDropdown from '@/app/components/share/text-generation/menu-dropdown'
import Confirm from '@/app/components/base/confirm'
import RenameModal from '@/app/components/base/chat/chat-with-history/sidebar/rename-modal'
import LogoSite from '@/app/components/base/logo/logo-site'
import type { ConversationItem } from '@/models/share'
import cn from '@/utils/classnames'
import styles from '../index.module.css'
import Robot from '@/assets/lark-app-robot.gif'
import Image from '@/app/components/base/image'
import Input from '../../../input'

const LarkDeleteIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" className={className}>
    <g stroke="#A3AFBB" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <path d="M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M0 2.04166667L9.33333333 2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 0.29166667L5.83333333 0.29166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 7.875L3.5 4.95833333" transform="translate(2.3333 1.4583)" />
      <path d="M5.83333333 7.875L5.83333333 4.95833333" transform="translate(2.3333 1.4583)" />
    </g>
  </svg>
)

type Props = {
  isPanel?: boolean
}

const Sidebar = ({ isPanel }: Props) => {
  const { t } = useTranslation()
  const {
    appData,
    handleNewConversation,
    pinnedConversationList,
    conversationList,
    currentConversationId,
    handleChangeConversation,
    handlePinConversation,
    handleUnpinConversation,
    conversationRenaming,
    handleRenameConversation,
    handleDeleteConversation,
    sidebarCollapseState,
    handleSidebarCollapse,
    isMobile,
    isResponding,
    embedSource,
    isFold,
    setIsFold,
    handleClearAllConversations
  } = useChatWithHistoryContext()
  const isSidebarCollapsed = sidebarCollapseState

  const [showConfirm, setShowConfirm] = useState<ConversationItem | null>(null)
  const [showClearAll, setShowClearAll] = useState<boolean | null>(null)
  const [showRename, setShowRename] = useState<ConversationItem | null>(null)
  const [keyword, setKeyword] = useState<string>('')
  const isEmbedMobile = isMobile && embedSource

  const handleOperate = useCallback((type: string, item: ConversationItem) => {
    if (type === 'pin')
      handlePinConversation(item.id)

    if (type === 'unpin')
      handleUnpinConversation(item.id)

    if (type === 'delete')
      setShowConfirm(item)

    if (type === 'rename')
      setShowRename(item)
  }, [handlePinConversation, handleUnpinConversation])
  const handleCancelConfirm = useCallback(() => {
    setShowConfirm(null)
  }, [])
  const handleDelete = useCallback(() => {
    if (showConfirm)
      handleDeleteConversation(showConfirm.id, { onSuccess: handleCancelConfirm })
  }, [showConfirm, handleDeleteConversation, handleCancelConfirm])
  const handleCancelRename = useCallback(() => {
    setShowRename(null)
  }, [])
  const handleRename = useCallback((newName: string) => {
    if (showRename)
      handleRenameConversation(showRename.id, newName, { onSuccess: handleCancelRename })
  }, [showRename, handleRenameConversation, handleCancelRename])

  const handleSearch = throttle(({ target }: ChangeEvent<HTMLInputElement>) => {
    setKeyword(target?.value)
  }, 100)

  const handleClearAll = () => {
    setShowClearAll(true)
  }

  const handleClearAllConfirm = () => {
    handleClearAllConversations?.({ onSuccess: () => setShowClearAll(false) })
  }

  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const aiRobotGifUrl = chatPageConfigData?.aiRobotGifUrl || Robot // AI机器人动画

  return (
    <div className={cn(
      'flex w-full grow flex-col',
      isPanel && 'rounded-xl border-[0.5px] border-components-panel-border-subtle bg-components-panel-bg shadow-lg',
      isEmbedMobile && styles.bg,
      isEmbedMobile && 'bg-[#f5f6f8] w-[74vw] !border-none'
    )}>
      <div className={cn(
        'flex shrink-0 items-center gap-3 p-3 pr-2',
      )}>
        <div className='shrink-0'>
          {
            embedSource ? (
              !isMobile && <div className="flex items-center">
                <Input
                  className='h-[40px] rounded-[8px] border-[1px] border-solid border-[#DFE4E8]'
                  value={keyword}
                  placeholder="搜索对话..."
                  showLeftIcon
                  showClearIcon
                  onChange={handleSearch}
                  onClear={() => setKeyword('')}
                />
                <BtnFold className="ml-[11px]" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />
              </div>
            ) : (
              <>
                <AppIcon
                  size='large'
                  iconType={appData?.site.icon_type}
                  icon={appData?.site.icon}
                  background={appData?.site.icon_background}
                  imageUrl={appData?.site.icon_url}
                />
                <div className={cn('system-md-semibold grow truncate text-text-secondary')}>{appData?.site.title}</div>
                {!isMobile && isSidebarCollapsed && (
                  <ActionButton size='l' onClick={() => handleSidebarCollapse(false)}>
                    <RiExpandRightLine className='h-[18px] w-[18px]' />
                  </ActionButton>
                )}
                {!isMobile && !isSidebarCollapsed && (
                  <ActionButton size='l' onClick={() => handleSidebarCollapse(true)}>
                    <RiLayoutLeft2Line className='h-[18px] w-[18px]' />
                  </ActionButton>
                )}
              </>
            )
          }
        </div>
      </div>
      {
        !embedSource && (
          <div className='shrink-0 px-3 py-4'>
            <Button variant='secondary-accent' disabled={isResponding} className='w-full justify-center' onClick={handleNewConversation}>
              <RiEditBoxLine className='mr-1 h-4 w-4' />
              {t('share.chat.newChat')}
            </Button>
          </div>
        )
      }
      {embedSource && isMobile && <div className="mb-[30px]">
        <Image src={aiRobotGifUrl} width={100} height={100} className="w-[100px] height-[100px] mt-[64px] mx-auto mb-[6px]" alt="" />
        <p className="text-center text-[18px] text-[#242933] mb-[20px] font-semibold">Hi～我是{appData?.site.title}</p>
        <div className="px-[12px]">
          <Input
            className='h-[40px] rounded-[8px] border-[1px] border-solid bg-[#fff] hover:bg-[#fff]'
            value={keyword}
            placeholder="搜索对话..."
            showLeftIcon
            showClearIcon
            onChange={handleSearch}
            onClear={() => setKeyword('')}
          />
        </div>
      </div>}
      <div className="flex items-center justify-between px-[20px] mb-[10px]">
        <p className="text-[16px] text-[#242933] font-medium">对话记录</p>
        <button onClick={handleClearAll}><LarkDeleteIcon className="w-[20px] h-[20px]" /></button>
      </div>
      <div className='h-0 grow space-y-2 overflow-y-auto px-3 pt-4'>
        {/* pinned list */}
        {!!pinnedConversationList.length && (
          <div className='mb-4'>
            <List
              embedSource={embedSource}
              isMobile={isMobile}
              isPin
              title={t('share.chat.pinnedTitle') || ''}
              list={pinnedConversationList.filter(item => item.name.includes(keyword))}
              onChangeConversation={handleChangeConversation}
              onOperate={handleOperate}
              currentConversationId={currentConversationId}
            />
          </div>
        )}
        {!!conversationList.length && (
          <List
            embedSource={embedSource}
            isMobile={isMobile}
            title={(pinnedConversationList.length && t('share.chat.unpinnedTitle')) || ''}
            list={conversationList.filter(item => item.name.includes(keyword))}
            onChangeConversation={handleChangeConversation}
            onOperate={handleOperate}
            currentConversationId={currentConversationId}
          />
        )}
      </div>
      <div className='flex shrink-0 items-center justify-between p-3'>
        <MenuDropdown placement='top-start' data={appData?.site} />
        {/* powered by */}
        <div className='shrink-0'>
          {!appData?.custom_config?.remove_webapp_brand && (
            <div className={cn(
              'flex shrink-0 items-center gap-1.5 px-2',
            )}>
              <div className='system-2xs-medium-uppercase text-text-tertiary'>{t('share.chat.poweredBy')}</div>
              {appData?.custom_config?.replace_webapp_logo && (
                <img src={appData?.custom_config?.replace_webapp_logo} alt='logo' className='block h-5 w-auto' />
              )}
              {!appData?.custom_config?.replace_webapp_logo && (
                <LogoSite className='!h-5' />
              )}
            </div>
          )}
        </div>
      </div>
      {!!showConfirm && (
        <Confirm
          title={embedSource && isMobile ? ' 确定要删除吗？' : t('share.chat.deleteConversation.title')}
          content={embedSource && isMobile ? "删除后无法撤销" : t('share.chat.deleteConversation.content') || ''}
          isShow
          isEmbedMobile={Boolean(embedSource && isMobile)}
          onCancel={handleCancelConfirm}
          onConfirm={handleDelete}
          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>
            <p className="py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]" onClick={handleCancelConfirm}>取消</p>
            <p className="py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]" onClick={handleDelete}>删除</p>
          </div>) : undefined}
        />
      )}
      {showClearAll && (
        <Confirm
          title={'确定要删除所有对话记录吗？'}
          content={"删除后无法撤销"}
          isShow
          isEmbedMobile={Boolean(embedSource && isMobile)}
          onCancel={() => setShowClearAll(false)}
          onConfirm={handleClearAllConfirm}
          footerRender={embedSource && isMobile ? (() => <div className='flex w-full border-t-[1px] border-t-[#EEF0F2] mt-[23px]'>
            <p className="py-[15px] text-[#A3AFBB] flex-1 text-center border-r-[1px] border-r-[#EEF0F2] text-[16px]" onClick={() => setShowClearAll(false)}>取消</p>
            <p className="py-[15px] text-[#FF4C4C] flex-1 text-center text-[16px]" onClick={handleClearAllConfirm}>删除</p>
          </div>) : undefined}
        />
      )}
      {showRename && (
        <RenameModal
          isEmbedMobile={Boolean(embedSource && isMobile)}
          isShow
          onClose={handleCancelRename}
          saveLoading={conversationRenaming}
          name={showRename?.name || ''}
          onSave={handleRename}
        />
      )}
    </div>
  )
}

export default Sidebar
