'use client'
import type { FC } from 'react'
import React, { useEffect, useRef, useState } from 'react'
import {
  RiDeleteBinLine,
  RiEditLine,
  RiMoreFill,
  RiPushpinLine,
  RiUnpinLine,
} from '@remixicon/react'
import { useTranslation } from 'react-i18next'
import { useBoolean } from 'ahooks'
import { PortalToFollowElem, PortalToFollowElemContent, PortalToFollowElemTrigger } from '@/app/components/base/portal-to-follow-elem'
import ActionButton, { ActionButtonState } from '@/app/components/base/action-button'
import cn from '@/utils/classnames'
import { EmbedSource } from '@/models/share'

const LarkRenameIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" className={className}>
    <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <path d="M9.33333333,4.66666667 L9.33333333,8.33333333 C9.33333333,8.88561808 8.88561808,9.33333333 8.33333333,9.33333333 L1,9.33333333 C0.44771525,9.33333333 3.38176876e-17,8.88561808 0,8.33333333 L0,1 C-6.76353751e-17,0.44771525 0.44771525,6.76353751e-17 1,0 L6.99195588,0 L6.99195588,0" transform="translate(2.3333 2.3333)" />
      <path transform="rotate(42 4.83572284 7.99757716)" d="M6.90262064 0.08444366L6.51404603 7.49888968" />
    </g>
  </svg>

)
const LarkPinIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" className={className}>
    <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <path d="M3,8 L3,1 M3,1 L0,3.8 M3,1 L6,3.8" transform="translate(4 3)" />
      <path d="M0 0.5L6 0.5" transform="translate(4 3)" />
    </g>
  </svg>

)
const LarkDeleteIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 14 14" className={className}>
    <g stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <path d="M8.75,2.04166667 L8.22889873,9.85818568 C8.19387888,10.3834835 7.75757745,10.7916667 7.23111357,10.7916667 L2.10221976,10.7916667 C1.57575588,10.7916667 1.13945446,10.3834835 1.1044346,9.85818568 L0.583333333,2.04166667 L0.583333333,2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M0 2.04166667L9.33333333 2.04166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 0.29166667L5.83333333 0.29166667" transform="translate(2.3333 1.4583)" />
      <path d="M3.5 7.875L3.5 4.95833333" transform="translate(2.3333 1.4583)" />
      <path d="M5.83333333 7.875L5.83333333 4.95833333" transform="translate(2.3333 1.4583)" />
    </g>
  </svg>
)

type Props = {
  isActive?: boolean
  isItemHovering?: boolean
  isPinned: boolean
  isShowRenameConversation?: boolean
  onRenameConversation?: () => void
  isShowDelete: boolean
  togglePin: () => void
  onDelete: () => void
  embedSource?: EmbedSource
  isMobile?: boolean
}

const Operation: FC<Props> = ({
  isActive,
  isItemHovering,
  isPinned,
  togglePin,
  isShowRenameConversation,
  onRenameConversation,
  isShowDelete,
  onDelete,
  embedSource,
  isMobile
}) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const ref = useRef(null)
  const [isHovering, { setTrue: setIsHovering, setFalse: setNotHovering }] = useBoolean(false)
  const isEmbedMobile = embedSource && isMobile

  useEffect(() => {
    if (!isItemHovering && !isHovering)
      setOpen(false)
  }, [isItemHovering, isHovering])

  return (
    <PortalToFollowElem
      open={open}
      onOpenChange={setOpen}
      placement='bottom-end'
      offset={4}
    >
      <PortalToFollowElemTrigger
        onClick={() => setOpen(v => !v)}
      >
        <ActionButton
          className={cn((isItemHovering || open) ? 'opacity-100' : 'opacity-0')}
          state={
            isActive
              ? ActionButtonState.Active
              : open
                ? ActionButtonState.Hover
                : ActionButtonState.Default
          }
        >
          <RiMoreFill className='h-4 w-4' />
        </ActionButton>
      </PortalToFollowElemTrigger>
      <PortalToFollowElemContent className="z-50">
        <div
          ref={ref}
          className={`min-w-[120px] rounded-xl border-[0.5px] border-components-panel-border bg-components-panel-bg-blur p-1 shadow-lg backdrop-blur-sm ${isEmbedMobile && 'flex !bg-[#262933] gap-[10px]'}`}
          onMouseEnter={setIsHovering}
          onMouseLeave={setNotHovering}
          onClick={(e) => {
            e.stopPropagation()
          }}
        >
          <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={togglePin}>
            {isPinned && <RiUnpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}
            {!isPinned && <RiPushpinLine className='h-4 w-4 shrink-0 text-text-tertiary' />}
            <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{isPinned ? t('explore.sidebar.action.unpin') : t('explore.sidebar.action.pin')}</span>
          </div>
          {isShowRenameConversation && (
            <div className={cn('system-md-regular flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-base-hover',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onRenameConversation}>
              <RiEditLine className='h-4 w-4 shrink-0 text-text-tertiary' />
              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.rename')}</span>
            </div>
          )}
          {isShowDelete && (
            <div className={cn('system-md-regular group flex cursor-pointer items-center space-x-1 rounded-lg px-2 py-1.5 text-text-secondary hover:bg-state-destructive-hover hover:text-text-destructive',isEmbedMobile && 'flex-col !h-auto !gap-[0px] !px-[0px]')} onClick={onDelete} >
              <RiDeleteBinLine className={cn('h-4 w-4 shrink-0 text-text-tertiary group-hover:text-text-destructive')} />
              <span className={cn('grow',isEmbedMobile && '!text-[#fff] !text-[12px]')}>{t('explore.sidebar.action.delete')}</span>
            </div>
          )}
        </div>
      </PortalToFollowElemContent>
    </PortalToFollowElem>
  )
}
export default React.memo(Operation)
