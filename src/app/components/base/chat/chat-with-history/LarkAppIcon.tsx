const Icon = ({ className }: { className: string }) => (
    <svg viewBox="0 0 124 32" className={className}>
        {/* <defs>
            <linearGradient x1="137.028673%" y1="127.53163%" x2="137.028673%" y2="0%" id="2ue80z9w0__rjmj8hx02a">
                <stop stop-color="#204FFF" offset="0%" />
                <stop stop-color="#71BCFF" offset="100%" />
            </linearGradient>
            <linearGradient x1="137.028673%" y1="127.53163%" x2="137.028673%" y2="0%" id="2ue80z9w0__qv5tly4itb">
                <stop stop-color="#204FFF" offset="0%" />
                <stop stop-color="#71BCFF" offset="100%" />
            </linearGradient>
            <linearGradient x1="137.028673%" y1="127.53163%" x2="137.028673%" y2="0%" id="2ue80z9w0__4javkq7asc">
                <stop stop-color="#204FFF" offset="0%" />
                <stop stop-color="#71BCFF" offset="100%" />
            </linearGradient>
            <linearGradient x1="137.028673%" y1="127.53163%" x2="137.028673%" y2="0%" id="2ue80z9w0__47qlwixisd">
                <stop stop-color="#204FFF" offset="0%" />
                <stop stop-color="#71BCFF" offset="100%" />
            </linearGradient>
            <linearGradient x1="100%" y1="100%" x2="0%" y2="0%" id="2ue80z9w0__yo6p6csm7f">
                <stop stop-color="#204FFF" offset="0%" />
                <stop stop-color="#71BCFF" offset="100%" />
            </linearGradient>
            <linearGradient x1="98.945638%" y1="50%" x2="0%" y2="50%" id="2ue80z9w0__856n2dlrzo">
                <stop stop-color="#4FB8FF" offset="0%" />
                <stop stop-color="#2C58F4" offset="100%" />
            </linearGradient>
            <filter x="-21.4%" y="-33.3%" width="142.9%" height="166.7%" filterUnits="objectBoundingBox" id="2ue80z9w0__hszo5q07si">
                <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner1" />
                <feOffset dx="-2" dy="-2" in="shadowBlurInner1" result="shadowOffsetInner1" />
                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1" />
                <feColorMatrix values="0 0 0 0 0.872008384 0 0 0 0 0.934235077 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner1" result="shadowMatrixInner1" />
                <feGaussianBlur stdDeviation="2" in="SourceAlpha" result="shadowBlurInner2" />
                <feOffset dx="-2" dy="-2" in="shadowBlurInner2" result="shadowOffsetInner2" />
                <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2" />
                <feColorMatrix values="0 0 0 0 0.705011433 0 0 0 0 0.831603172 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner2" result="shadowMatrixInner2" />
                <feGaussianBlur stdDeviation="1.5" in="SourceAlpha" result="shadowBlurInner3" />
                <feOffset dy="2" in="shadowBlurInner3" result="shadowOffsetInner3" />
                <feComposite in="shadowOffsetInner3" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner3" />
                <feColorMatrix values="0 0 0 0 0.326828301 0 0 0 0 0.549603196 0 0 0 0 0.92578125 0 0 0 0.5 0" in="shadowInnerInner3" result="shadowMatrixInner3" />
                <feMerge>
                    <feMergeNode in="shadowMatrixInner1" />
                    <feMergeNode in="shadowMatrixInner2" />
                    <feMergeNode in="shadowMatrixInner3" />
                </feMerge>
            </filter>
            <filter x="-60%" y="-85.7%" width="220%" height="271.4%" filterUnits="objectBoundingBox" id="2ue80z9w0__qdq8y0e7nj">
                <feGaussianBlur stdDeviation="2" in="SourceGraphic" />
            </filter>
            <filter x="-6.1%" y="-6.2%" width="112.1%" height="112.5%" filterUnits="objectBoundingBox" id="2ue80z9w0__ejs5t4nzfl">
                <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner1" />
                <feOffset dx="-1" dy="-1" in="shadowBlurInner1" result="shadowOffsetInner1" />
                <feComposite in="shadowOffsetInner1" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner1" />
                <feColorMatrix values="0 0 0 0 0.636737805 0 0 0 0 0.778220333 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner1" result="shadowMatrixInner1" />
                <feGaussianBlur stdDeviation="1" in="SourceAlpha" result="shadowBlurInner2" />
                <feOffset dy="1" in="shadowBlurInner2" result="shadowOffsetInner2" />
                <feComposite in="shadowOffsetInner2" in2="SourceAlpha" operator="arithmetic" k2="-1" k3="1" result="shadowInnerInner2" />
                <feColorMatrix values="0 0 0 0 0.702782012 0 0 0 0 0.815424122 0 0 0 0 1 0 0 0 1 0" in="shadowInnerInner2" result="shadowMatrixInner2" />
                <feMerge>
                    <feMergeNode in="shadowMatrixInner1" />
                    <feMergeNode in="shadowMatrixInner2" />
                </feMerge>
            </filter>
            <filter x="-23.8%" y="-33.9%" width="147.5%" height="167.9%" filterUnits="objectBoundingBox" id="2ue80z9w0__c5o2cu5k4m">
                <feGaussianBlur stdDeviation="1.584" in="SourceGraphic" />
            </filter>
            <filter x="-173.1%" y="-154.4%" width="446.2%" height="408.9%" filterUnits="objectBoundingBox" id="2ue80z9w0__5wnwg0o5qp">
                <feGaussianBlur stdDeviation="2.376" in="SourceGraphic" />
            </filter>
            <filter x="-173.1%" y="-154.4%" width="446.2%" height="408.9%" filterUnits="objectBoundingBox" id="2ue80z9w0__h0t79ej6zr">
                <feGaussianBlur stdDeviation="2.376" in="SourceGraphic" />
            </filter>
            <path id="2ue80z9w0__ql92qr3nae" d="M16 0A16 16 0 1 0 16 32A16 16 0 1 0 16 0Z" />
            <path d="M19.9619809,0 C21.9339879,0 19.9619809,1.14941037 19.9619809,2.5665621 C19.9619809,3.98371383 21.2318421,2.74413863 22.3272904,2.5665621 C23.2626197,2.41494149 23.3993345,3.05067933 22.1570479,4.22608108 C24.0079482,5.0420239 25.4151483,6.72128285 25.8245417,8.80914415 C26.1766388,10.6084298 26.3529412,12.2873999 26.3529412,13.8461539 C26.3529412,15.4026011 26.1759859,17.0824546 25.8220752,18.8857142 C25.2378691,21.8572996 22.632644,24.0002437 19.6041763,24 L8.25458399,24 C5.11705757,23.9997195 2.45224381,21.7032132 1.98868608,18.6001202 C1.76102121,17.0772288 1.64705882,15.6630591 1.64705882,14.3575721 C1.64705882,12.983695 1.77327419,11.4035158 2.02570492,9.61703445 L2.10548101,9.07494061 C2.57659759,5.97958515 5.23801375,3.69247 8.36901617,3.69230771 L14.5368613,3.69267218 C16.9289408,1.23089073 18.737314,0 19.9619809,0 Z" id="2ue80z9w0__7cpr4p4uek" />
            <path d="M3.96,0 L14.1576471,0 C16.3446947,-4.01754128e-16 18.1176471,1.77295239 18.1176471,3.96 L18.117647,5.61147837 C18.117948,7.79852596 16.3452395,9.57172229 14.158192,9.57202323 C13.7680827,9.57207691 13.3801066,9.51448608 13.0068338,9.40111667 C10.9703903,8.78272467 9.65439061,8.47351035 9.05882353,8.47351035 C8.46325644,8.47351035 7.14725675,8.78272467 5.11082443,9.40115332 L5.1108133,9.40111667 C3.0181548,10.0366933 0.806483242,8.85549507 0.170906606,6.76283657 C0.0575371881,6.38956376 -5.36419305e-05,6.0015876 3.74893889e-08,5.61147837 L0,3.96 C-2.67836085e-16,1.77295239 1.77295239,2.67836085e-16 3.96,0 Z" id="2ue80z9w0__vyssbjs3on" />
            <rect id="2ue80z9w0__qcg1a3bbfh" x="9" y="27" width="14" height="9" rx="4.5" />
        </defs>
        <g fill="none" fill-rule="evenodd">
            <g fill-rule="nonzero">
                <path d="M0,19.5970308 L0,19.5970308 L0,19.5970308 C1.17772449,19.5970308 2.2584599,19.1975963 3.24220624,18.3987275 C4.22595257,17.5998586 4.87023714,16.5924355 5.17505995,15.3764581 L8.39648281,1.31495228 L4.88409273,1.31495228 C4.69011458,1.31495228 4.5134559,1.38211382 4.35411671,1.5164369 C4.19477751,1.65075999 4.09432454,1.81689643 4.05275779,2.01484624 L0,19.5970308 Z M17.4372502,0 L17.4372502,0 L14.028777,0 C13.7516653,0 13.5057288,0.0954400848 13.2909672,0.286320255 C13.0762057,0.477200424 12.9480416,0.714033227 12.9064748,0.996818664 L10.6826539,16.9459173 C10.6410871,17.1862849 10.5371703,17.3700954 10.3709033,17.4973489 C10.1907807,17.6104631 9.97601918,17.6670201 9.72661871,17.6670201 L8.31334932,17.6670201 C8.00852651,17.6670201 7.73487876,17.7730647 7.49240608,17.9851538 C7.24993339,18.1972428 7.10098588,18.4729586 7.04556355,18.8123012 L6.87929656,20 L12.1790568,20 C12.5115907,20 12.8302691,19.9505125 13.1350919,19.8515376 C13.4537703,19.7384235 13.7447375,19.5758218 14.0079936,19.3637328 C14.2573941,19.1516437 14.4721556,18.8971368 14.6522782,18.6002121 C14.8324007,18.2891481 14.957101,17.9427359 15.0263789,17.5609756 L17.4372502,0 Z M21.0743405,16.6277837 L21.0743405,16.6277837 C21.0604849,17.4902793 21.3133493,18.2007777 21.8329337,18.7592789 C22.352518,19.3177801 23.0349054,19.5970308 23.8800959,19.5970308 L25.1270983,19.5970308 L26,1.31495228 L22.9240608,1.31495228 C22.6192379,1.31495228 22.3559819,1.42806645 22.1342926,1.6542948 C21.9126033,1.88052315 21.787903,2.14916932 21.7601918,2.4602333 L21.0743405,16.6277837 Z" fill="url(#2ue80z9w0__rjmj8hx02a)" transform="translate(43 6)" />
                <path d="M30.0126883,4.51271186 L30.0126883,4.51271186 L30.1197462,3.77118644 L34.9801745,4.70338983 L34.7232355,6.67372881 L30.9547978,5.95338983 C30.6264869,5.88276836 30.3766852,5.72033898 30.2053925,5.46610169 C30.0340999,5.19774011 29.9698652,4.8799435 30.0126883,4.51271186 Z M28,20 L28,20 L30.4623315,7.73305085 L34.5947661,7.73305085 L32.4321967,18.0084746 C32.3037272,18.5734463 32.0325139,19.039548 31.6185567,19.4067797 C31.2045995,19.8022599 30.740682,20 30.2268041,20 L28,20 Z M30.4837431,0.995762712 L30.4837431,0.995762712 L30.5693894,0.254237288 L35.4298176,1.18644068 L35.1728787,3.15677966 L31.4044409,2.43644068 C31.0904044,2.36581921 30.8477399,2.19632768 30.6764473,1.9279661 C30.5051546,1.65960452 30.4409199,1.34887006 30.4837431,0.995762712 Z M41.4250595,2.35169492 L41.4250595,2.35169492 L41.4250595,2.35169492 C41.0396511,3.02966102 40.5329104,3.36864407 39.9048374,3.36864407 L36.3505155,3.36864407 L37.8493259,0.31779661 C37.963521,0.105932203 38.1205393,0 38.3203807,0 L42.6026963,0 L42.2386994,0.677966102 L55,0.677966102 L54.8715305,1.69491525 C54.8287074,1.87853107 54.7359239,2.03389831 54.59318,2.16101695 C54.4504362,2.28813559 54.2791435,2.35169492 54.0793021,2.35169492 L41.4250595,2.35169492 Z M52.6233148,10.2542373 L52.6233148,10.2542373 L53.8223632,10.2542373 L53.629659,11.5677966 C53.6011102,11.7372881 53.5083267,11.8679379 53.3513085,11.9597458 C53.1942902,12.0515537 53.037272,12.0974576 52.8802538,12.0974576 L52.3663759,12.0974576 L51.6169707,17.4152542 C51.5741475,17.7118644 51.4885012,18.0084746 51.3600317,18.3050847 C51.2315623,18.5875706 51.0459952,18.8488701 50.8033307,19.0889831 C50.5606661,19.3149718 50.2537669,19.4985876 49.8826328,19.6398305 C49.5114988,19.7669492 49.0761301,19.8305085 48.5765266,19.8305085 L37.7208565,19.8305085 C37.221253,19.8305085 36.8072958,19.7669492 36.4789849,19.6398305 C36.1506741,19.4985876 35.8937351,19.3149718 35.7081681,19.0889831 C35.5226011,18.8629944 35.4012688,18.6016949 35.3441713,18.3050847 C35.2870738,18.0084746 35.2727994,17.7118644 35.3013481,17.4152542 L36.0721649,12.0974576 L35.0444092,12.0974576 L35.2157018,10.9110169 C35.2442506,10.7415254 35.3263283,10.5896893 35.461935,10.4555085 C35.5975416,10.3213277 35.7509913,10.2542373 35.9222839,10.2542373 L36.3291039,10.2542373 L37.2283902,3.87711864 L41.6391753,3.87711864 L40.739889,10.2542373 L48.2125297,10.2542373 L48.8762887,5.55084746 L42.0888184,5.55084746 L42.3243458,3.87711864 L53.5226011,3.87711864 L52.6233148,10.2542373 Z M47.184774,17.4152542 L47.184774,17.4152542 L47.9555908,12.0974576 L40.48295,12.0974576 L39.7121332,17.4152542 C39.6978588,17.6412429 39.7620936,17.8248588 39.9048374,17.9661017 C40.0618557,18.0932203 40.2545599,18.1567797 40.48295,18.1567797 L46.3068993,18.1567797 C46.5352895,18.1567797 46.7279937,18.0932203 46.8850119,17.9661017 C47.0563045,17.8248588 47.1562252,17.6412429 47.184774,17.4152542 Z M45.9857256,17.4788136 L45.9857256,17.4788136 L42.9024584,17.4788136 C42.6169707,17.4788136 42.3743061,17.3905367 42.1744647,17.2139831 C41.9746233,17.0374294 41.8604282,16.8008475 41.8318795,16.5042373 L41.5535289,12.7754237 L44.7010309,12.7754237 C44.9722443,12.7754237 45.1970658,12.8566384 45.3754956,13.0190678 C45.5539255,13.1814972 45.6574148,13.3968927 45.6859635,13.6652542 L45.9857256,17.4788136 Z M46.9920698,9.25847458 L46.9920698,9.25847458 L42.8596352,9.25847458 L42.6026963,6.39830508 L45.9214909,6.39830508 C46.1641554,6.39830508 46.3675654,6.46892655 46.5317209,6.61016949 C46.6958763,6.75141243 46.7850912,6.9420904 46.7993656,7.18220339 L46.9920698,9.25847458 Z" fill="url(#2ue80z9w0__qv5tly4itb)" transform="translate(43 6)" />
                <path d="M60.1287998,16.2543353 L60.1287998,16.2543353 L59.0535718,18.5225434 C58.4801168,19.5075145 57.6772799,20 56.645061,20 L54,20 C54,20 54.3010638,19.4208092 54.9031915,18.2624277 C55.5053192,17.1040462 56.2472266,15.6751445 57.1289136,13.9757225 C58.0106006,12.2763006 58.9030398,10.5560694 59.8062314,8.8150289 C60.7094229,7.07398844 61.4907553,5.57225434 62.1502285,4.30982659 C63.0820928,2.7699422 64.3651983,2 65.9995449,2 L68.0639827,2 C68.0783191,2.05549133 68.228851,2.67283237 68.5155785,3.85202312 C68.802306,5.03121387 69.1571312,6.49479769 69.5800542,8.24277457 C70.0029773,9.99075145 70.4294844,11.7456647 70.8595756,13.5075145 C71.2896668,15.2693642 71.6552443,16.7606936 71.9563082,17.9815029 C72.0709992,18.5086705 71.9598923,18.9768786 71.6229875,19.3861272 C71.2860827,19.7953757 70.8452392,20 70.300457,20 L68.0854873,20 L67.3543322,16.2543353 L60.1287998,16.2543353 Z M65.2898944,5.32947977 L65.2898944,5.32947977 L61.4190734,13.3202312 L66.8597273,13.3202312 L65.3113989,5.32947977 L65.2898944,5.32947977 Z" fill="url(#2ue80z9w0__4javkq7asc)" transform="translate(43 6)" />
                <path d="M74,20 L74,20 L76.5145631,3.66089965 C76.5900755,3.17647059 76.8279396,2.77854671 77.2281553,2.46712803 C77.6283711,2.15570934 78.0927724,2 78.6213592,2 L81,2 C81,2 81,2 81,2 C81,2 81,2 81,2 L78.5307443,18.0484429 C78.4401294,18.615917 78.1645092,19.083045 77.7038835,19.449827 C77.2432578,19.816609 76.7033441,20 76.0841424,20 L74,20 Z" fill="url(#2ue80z9w0__47qlwixisd)" transform="translate(43 6)" />
            </g>
            <g>
                <mask id="2ue80z9w0__r1xhj3vsvg" fill="#fff">
                    <use xlink:href="#2ue80z9w0__ql92qr3nae" />
                </mask>
                <use fill="url(#2ue80z9w0__yo6p6csm7f)" fill-rule="nonzero" xlink:href="#2ue80z9w0__ql92qr3nae" />
                <g mask="url(#2ue80z9w0__r1xhj3vsvg)">
                    <use fill="#F4F7FF" xlink:href="#2ue80z9w0__qcg1a3bbfh" />
                    <use fill="#000" filter="url(#2ue80z9w0__hszo5q07si)" xlink:href="#2ue80z9w0__qcg1a3bbfh" />
                </g>
                <rect fill="#FFF" filter="url(#2ue80z9w0__qdq8y0e7nj)" mask="url(#2ue80z9w0__r1xhj3vsvg)" x="11" y="28" width="10" height="7" rx="3.5" />
            </g>
            <g transform="translate(2 3)">
                <rect fill="#DFE9FF" y="11.0769231" width="3.29411765" height="6.46153847" rx="1.64705882" />
                <rect fill="#DFE9FF" x="24.7058824" y="11.0769231" width="3.29411765" height="6.46153847" rx="1.64705882" />
                <use fill="#ECF4FF" xlink:href="#2ue80z9w0__7cpr4p4uek" />
                <use fill="#000" filter="url(#2ue80z9w0__ejs5t4nzfl)" xlink:href="#2ue80z9w0__7cpr4p4uek" />
                <rect fill="#FFF" filter="url(#2ue80z9w0__c5o2cu5k4m)" x="3.94117647" y="7.46153847" width="20" height="14" rx="5.94000006" />
                <g transform="translate(4.9412 8.2308)">
                    <mask id="2ue80z9w0__6h7lxudh8q" fill="#fff">
                        <use xlink:href="#2ue80z9w0__vyssbjs3on" />
                    </mask>
                    <path stroke="url(#2ue80z9w0__856n2dlrzo)" stroke-width=".792" d="M14.1576471,0.396 C15.1418185,0.396 16.0328185,0.794914288 16.6777756,1.43987143 C17.3227328,2.08482858 17.7216471,2.97582858 17.7216471,3.95999999 L17.721647,5.61153286 C17.7217824,6.59570428 17.3229908,7.48675916 16.6781224,8.13180504 C16.033254,8.77685093 15.1423089,9.17588782 14.1581375,9.17602324 C13.8070392,9.17607155 13.4578606,9.1242398 13.1218962,9.02220158 C11.0261018,8.38578692 9.67132264,8.07751035 9.05882353,8.07751035 C8.47739946,8.07751035 7.22838221,8.35428599 5.3114137,8.9271319 C4.23126922,9.25483052 3.19317449,9.16617336 2.34870678,8.75751672 C1.49253936,8.34319833 0.839216986,7.60061838 0.549815946,6.64775524 C0.447783469,6.31180971 0.395951722,5.96263117 0.396000037,5.61147836 L0.396,3.96 C0.396,2.97582858 0.794914288,2.08482858 1.43987143,1.43987143 C2.08482858,0.794914288 2.97582858,0.396 3.96,0.396 L14.1576471,0.396 Z" />
                    <path fill="#7CD0FF" opacity=".92366536" filter="url(#2ue80z9w0__5wnwg0o5qp)" mask="url(#2ue80z9w0__6h7lxudh8q)" d="M4.05882353 5A2.05882353 2.30769231 0 1 0 4.05882353 9.61538462A2.05882353 2.30769231 0 1 0 4.05882353 5Z" />
                    <path fill="#7CD0FF" opacity=".92366536" filter="url(#2ue80z9w0__h0t79ej6zr)" mask="url(#2ue80z9w0__6h7lxudh8q)" d="M15.0588235 5A2.05882353 2.30769231 0 1 0 15.0588235 9.61538462A2.05882353 2.30769231 0 1 0 15.0588235 5Z" />
                </g>
                <rect fill="#97C3FF" x="9.05882353" y="10.0769231" width="2.47058824" height="4.61538462" rx="1.23529412" />
                <rect fill="#97C3FF" x="16.4705882" y="10.0769231" width="2.47058824" height="4.61538462" rx="1.23529412" />
                <path d="M12.3529412,19.3092015 C12.3529412,19.3092015 13.8839361,21.1493186 15.6470588,19.3092015" stroke="#005DD9" stroke-width=".792" stroke-linecap="round" stroke-linejoin="round" />
            </g>
        </g> */}
    </svg>
)

const LarkAppIcon = ({ className }: { className: string }) => (
    <Icon />
)

export default LarkAppIcon
