import { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import Image from '@/app/components/base/image'
import Chat from '../chat'
import type {
  ChatConfig,
  ChatItem,
  ChatItemInTree,
  OnSend,
} from '../types'
import { useChat } from '../chat/hooks'
import { getLastAnswer, isValidGeneratedAnswer } from '../utils'
import Button from '../../button'
import { useChatWithHistoryContext } from './context'
import { InputVarType } from '@/app/components/workflow/types'
import { TransferMethod } from '@/types/app'
import InputsForm from '@/app/components/base/chat/chat-with-history/inputs-form'
import BtnFold from './btn-fold'
import styles from './index.module.css'
import { EMBED_SOURCE_TYPE } from '@/config'
import {
  fetchInstitutionApi,
  fetchSuggestedQuestions,
  getUrl,
  stopChatMessageResponding,
} from '@/service/share'
import AppIcon from '@/app/components/base/app-icon'
import AnswerIcon from '@/app/components/base/answer-icon'
import SuggestedQuestions from '@/app/components/base/chat/chat/answer/suggested-questions'
import { Markdown } from '@/app/components/base/markdown'
import cn from '@/utils/classnames'
import type { FileEntity } from '../../file-uploader/types'
import LarkAppLogo from '@/assets/lark-app-logo.svg'
import { INTENT_DOCUMENT_ANALYSIS, INTENT_FREE_TALK, INTENT_INSTITUTION, INTENT_MEETING, LLM_QWQ, LOCATION_ZJ, NEED_CREATE_MEETING } from '@/app/components/base/chat/chat/u-const'
const NewChatIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <defs>
      <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="ca3toeybn__hi9n9hsm3a">
        <stop stop-color="#40A4FF" offset="0%" />
        <stop stop-color="#1E6AFF" offset="100%" />
      </linearGradient>
    </defs>
    <path d="M9,12 L9,9 M9,9 L9,6 M9,9 L6,9 M9,9 L12,9 M9.0001,18 C7.365,18 5.83174,17.5639 4.51025,16.8018 C4.3797,16.7265 4.31434,16.6888 4.25293,16.6719 C4.19578,16.6561 4.14475,16.6507 4.08559,16.6548 C4.02253,16.6591 3.9573,16.6808 3.82759,16.7241 L1.51807,17.4939 L1.51625,17.4947 C1.02892,17.6572 0.7848,17.7386 0.62256,17.6807 C0.4812,17.6303 0.36979,17.5187 0.31938,17.3774 C0.26157,17.2152 0.34268,16.9719 0.50489,16.4853 L0.50586,16.4823 L1.27468,14.1758 L1.27651,14.171 C1.31936,14.0424 1.34106,13.9773 1.34535,13.9146 C1.3494,13.8554 1.34401,13.804 1.32821,13.7469 C1.31146,13.6863 1.27448,13.6221 1.20114,13.495 L1.19819,13.4899 C0.43604,12.1684 0,10.6351 0,9 C0,4.02944 4.02944,0 9,0 C13.9706,0 18,4.02944 18,9 C18,13.9706 13.9707,18 9.0001,18 Z" transform="translate(1 1)" stroke="url(#ca3toeybn__hi9n9hsm3a)" stroke-width="1.6" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
)

const ChatWrapper = () => {
  const { t } = useTranslation()
  const {
    appParams,
    appPrevChatTree,
    currentConversationId,
    currentConversationItem,
    currentConversationInputs,
    inputsForms,
    newConversationInputsRef,
    handleNewConversation,
    handleNewConversationCompleted,
    isMobile,
    isInstalledApp,
    appId,
    appMeta,
    handleFeedback,
    currentChatInstanceRef,
    appData,
    themeBuilder,
    sidebarCollapseState,
    clearChatList,
    setClearChatList,
    setIsResponding,
    embedSource,
    isFold,
    setIsFold,
    larkInfo,
  } = useChatWithHistoryContext()
  let {
    newConversationInputs,
  } = useChatWithHistoryContext()
  const aiToolbarsConfig = appData?.site?.extension_data ? JSON.parse(appData.site.extension_data) : {}
  // 根据selected过滤出已选数据 没有selected的是以前老数据 !Object.hasOwn(item, 'selected')判断
  const aiToolbars = aiToolbarsConfig?.aiToolbars?.filter((item: any) => !Object.hasOwn(item, 'selected') || (Object.hasOwn(item, 'selected') && item?.selected))
  if (!newConversationInputs?.intent && !!embedSource)
    newConversationInputs = Object.assign(newConversationInputs, { intent: INTENT_FREE_TALK })

  const appConfig = useMemo(() => {
    const config = appParams || {}

    return {
      ...config,
      file_upload: {
        ...(config as any).file_upload,
        fileUploadConfig: (config as any).system_parameters,
      },
      embedSource,
      supportFeedback: true,
      opening_statement: currentConversationId ? currentConversationItem?.introduction : (config as any).opening_statement,
    } as ChatConfig
  }, [appParams, currentConversationItem?.introduction, currentConversationId, embedSource])
  const {
    chatList,
    setTargetMessageId,
    handleSend,
    handleStop,
    isResponding: respondingState,
    suggestedQuestions,
    currentIntent,
    currentLlm,
    updateIntent,
    updateLlm,
    identifyingIntent,
    updateIntentSilent,
    updateLastChatIntent,
  } = useChat(
    appConfig,
    {
      inputs: (currentConversationId ? currentConversationInputs : newConversationInputs) as any,
      inputsForm: inputsForms,
    },
    appPrevChatTree,
    taskId => stopChatMessageResponding('', taskId, isInstalledApp, appId),
    clearChatList,
    (bool) => {
      setClearChatList(bool)
      // if (!chatList.length && embedSource)
      //   updateIntent?.(LLM_DEFAULT_INTENT.label, LLM_DEFAULT_INTENT.prompt)
    },
    aiToolbars,
  )
  const inputsFormValue = currentConversationId ? currentConversationInputs : newConversationInputsRef?.current
  const inputDisabled = useMemo(() => {
    let hasEmptyInput = ''
    let fileIsUploading = false
    const requiredVars = inputsForms.filter(({ required }) => required)

    if(!embedSource) {
      if (requiredVars.length) {
        requiredVars.forEach(({ variable, label, type }) => {
          if (hasEmptyInput)
            return

          if (fileIsUploading)
            return

          if (!inputsFormValue?.[variable])
            hasEmptyInput = label as string

          if ((type === InputVarType.singleFile || type === InputVarType.multiFiles) && inputsFormValue?.[variable]) {
            const files = inputsFormValue[variable]
            if (Array.isArray(files))
              fileIsUploading = files.find(item => item.transferMethod === TransferMethod.local_file && !item.uploadedId)
            else
              fileIsUploading = files.transferMethod === TransferMethod.local_file && !files.uploadedId
          }
        })
      }
      if (hasEmptyInput)
        return true
    }

    if (fileIsUploading)
      return true
    return false
  }, [inputsFormValue, inputsForms])

  useEffect(() => {
    if (currentChatInstanceRef.current)
      currentChatInstanceRef.current.handleStop = handleStop
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    setIsResponding(respondingState)
  }, [respondingState, setIsResponding])

  const doSend: OnSend = useCallback((message, files, isRegenerate = false, parentAnswer: ChatItem | null = null, extract = null) => {
    const data: any = {
      query: message,
      files,
      inputs: currentConversationId ? currentConversationInputs : newConversationInputs,
      conversation_id: currentConversationId,
      parent_message_id: (isRegenerate ? parentAnswer?.id : getLastAnswer(chatList)?.id) || null,
      user: larkInfo?.user_id,
      embedSource,
    }

    if (embedSource) {
      data.inputs = {}

      currentIntent.current && (data.inputs.intent = currentIntent.current)
      currentLlm.current && (data.inputs.llm = currentLlm.current)

      // 文档分析固定传qwq模型
      currentIntent.current === INTENT_DOCUMENT_ANALYSIS && (data.inputs.llm = LLM_QWQ)

      // 提交日程
      if (extract && extract.event === 'create_calendar_submit') {
        data.inputs.schedule_info = JSON.stringify(
          extract,
        )
      }
      // 日程同步创建会议
      if (extract && extract.event === NEED_CREATE_MEETING) {
        const createMeetingData = {
          title: extract?.data?.subject,
          meeting_start: extract?.data?.start_time,
          meeting_end: extract?.data?.end_time,
          create_meeting_submit: 'create_meeting_success',
          attendees_ids: extract?.data?.attendees,
          attendees_names: extract?.data?.attendees_names,
          meeting_location: LOCATION_ZJ,
        }
        data.inputs.intent = INTENT_MEETING
        data.inputs.meeting_info = JSON.stringify(createMeetingData)
      }
      // 提交会议
      if (extract && extract.create_meeting_submit === 'create_meeting_success') {
        data.inputs.meeting_info = JSON.stringify(
          extract,
        )
      }
    }

    // 调用意图识别
    // if (!currentIntent.current && embedSource) {
    //   identifyingIntent(message, (msg) => {
    //     doSend(msg)
    //   })
    // }
    // else {
    handleSend(
      getUrl('chat-messages', isInstalledApp, appId || ''),
      data,
      {
        onGetSuggestedQuestions: (responseItemId) => {
          if (currentIntent.current?.includes(INTENT_INSTITUTION))
            return fetchInstitutionApi({ question: message })
          else
            return fetchSuggestedQuestions(responseItemId, isInstalledApp, appId)
        },
        onConversationComplete: currentConversationId ? undefined : handleNewConversationCompleted,
        isPublicAPI: !isInstalledApp,
        isIntentRecognition: extract?.isIntentRecognition,
      },
    )
  }, [chatList, handleNewConversationCompleted, handleSend, currentConversationId, currentConversationInputs, newConversationInputs, isInstalledApp, appId, embedSource, currentIntent.current])

  const doRegenerate = useCallback((chatItem: ChatItemInTree, editedQuestion?: { message: string, files?: FileEntity[] }) => {
    const question = editedQuestion ? chatItem : chatList.find(item => item.id === chatItem.parentMessageId)!
    const parentAnswer = chatList.find(item => item.id === question.parentMessageId)
    doSend(editedQuestion ? editedQuestion.message : question.content,
      editedQuestion ? editedQuestion.files : question.message_files,
      true,
      isValidGeneratedAnswer(parentAnswer) ? parentAnswer : null,
    )
  }, [chatList, doSend])

  const messageList = useMemo(() => {
    if (currentConversationId)
      return chatList
    return chatList.filter(item => !item.isOpeningStatement)
  }, [chatList, currentConversationId])

  const [collapsed, setCollapsed] = useState(!!currentConversationId)

  const ShowLogoContent = (url: string) => {
    if (!url)
      return embedSource === EMBED_SOURCE_TYPE.FS ? <Image src={LarkAppLogo} className='h-[34px]' alt='logo'/> : <div className='h-[34px]'></div>
    else
      return <img src={url} className='h-[34px]' alt="logo" />
  }

  const chatPageConfigData = appData?.site?.extension_data ? JSON.parse(appData?.site?.extension_data) : {}
  const aiTopLogoUrl = chatPageConfigData?.aiTopLogoUrl || ''
  const pageBgColorFrom = chatPageConfigData?.aiPageBgGradientFrom || 'rgb(215, 218, 252)'
  const pageBgColorTo = chatPageConfigData?.aiPageBgGradientTo || 'rgb(250, 250, 253)'
  const chatNode = useMemo(() => {
    if (embedSource && !isMobile) {
      return (
        <div className={`relative sticky top-0 z-10 flex shrink-0 overflow-hidden px-[15.8vw] py-[16px] ${styles.navBarBg}`}
             style={{
               // 注入 CSS 变量
               '--start-color': pageBgColorFrom,
               '--end-color': pageBgColorTo,
             }}>
          {isFold && <BtnFold className="absolute left-[10px] top-1/2 mr-[10px] flex -translate-y-1/2 items-center" isFold={isFold} onClick={() => setIsFold?.(!isFold)} />}

          <Button
            variant='secondary-accent'
            className={`absolute ${isFold ? 'left-[128px]' : 'left-[12px]'}  z-11 top-1/2 h-[40px] w-[123px] -translate-y-1/2 justify-start rounded-[20px] border-[#356CFF] text-[#434B5B]`}
            onClick={handleNewConversation}>
            <NewChatIcon className="mr-[8px] w-[18px]" />
            开始新对话
          </Button>
          <div className='flex flex-1 items-center justify-center'>
            <div className="flex items-center">
              {ShowLogoContent(aiTopLogoUrl)}
            </div>
          </div>
        </div>
      )
    }

    if (!inputsForms.length || embedSource)
      return null
    if (isMobile) {
      if (!currentConversationId)
        return <InputsForm collapsed={collapsed} setCollapsed={setCollapsed} />
      return null
    }
    else {
      return <InputsForm collapsed={collapsed} setCollapsed={setCollapsed} />
    }
  }, [inputsForms.length, isMobile, currentConversationId, collapsed, isFold])

  const welcome = useMemo(() => {
    const welcomeMessage = chatList.find(item => item.isOpeningStatement)
    if (respondingState)
      return null
    if (currentConversationId)
      return null
    if (!welcomeMessage)
      return null
    if (!collapsed && inputsForms.length > 0)
      return null
    if (welcomeMessage.suggestedQuestions && welcomeMessage.suggestedQuestions?.length > 0) {
      return (
        <div className='flex min-h-[50vh] items-center justify-center px-4 py-12'>
          <div className='flex max-w-[720px] grow gap-4'>
            <AppIcon
              size='xl'
              iconType={appData?.site.icon_type}
              icon={appData?.site.icon}
              background={appData?.site.icon_background}
              imageUrl={appData?.site.icon_url}
            />
            <div className='w-0 grow'>
              <div className='body-lg-regular grow rounded-2xl bg-chat-bubble-bg px-4 py-3 text-text-primary'>
                <Markdown content={welcomeMessage.content} />
                <SuggestedQuestions item={welcomeMessage} />
              </div>
            </div>
          </div>
        </div>
      )
    }
    return (
      <div className={cn('flex h-[50vh] flex-col items-center justify-center gap-3 py-12')}>
        <AppIcon
          size='xl'
          iconType={appData?.site.icon_type}
          icon={appData?.site.icon}
          background={appData?.site.icon_background}
          imageUrl={appData?.site.icon_url}
        />
        <div className='max-w-[768px] px-4'>
          <Markdown className='!body-2xl-regular !text-text-tertiary' content={welcomeMessage.content} />
        </div>
      </div>
    )
  }, [appData?.site.icon, appData?.site.icon_background, appData?.site.icon_type, appData?.site.icon_url, chatList, collapsed, currentConversationId, inputsForms.length, respondingState, embedSource, isFold])

  const answerIcon = (appData?.site && appData.site.use_icon_as_answer_icon)
    ? <AnswerIcon
      iconType={appData.site.icon_type}
      icon={appData.site.icon}
      background={embedSource ? 'transparent' : appData.site.icon_background}
      imageUrl={appData.site.icon_url}
    />
    : null

  return (
    <div
      className='h-full overflow-hidden bg-chatbot-bg'
    >
      <Chat
        appData={appData}
        config={appConfig}
        chatList={messageList}
        isResponding={respondingState}
        chatContainerInnerClassName={`mx-auto pt-6 w-full max-w-[768px] ${embedSource && 'px-[15.8vw] max-w-none'} ${isMobile && 'px-4'}`}
        chatFooterClassName='pb-4'
        chatFooterInnerClassName={`mx-auto w-full max-w-[768px] ${isMobile ? 'px-2' : 'px-4'} ${embedSource && 'max-w-none px-0'}`}
        onSend={doSend}
        inputs={currentConversationId ? currentConversationInputs as any : newConversationInputs}
        inputsForm={inputsForms}
        onRegenerate={doRegenerate}
        onStopResponding={handleStop}
        chatNode={
          <>
            {chatNode}
            {welcome}
          </>
        }
        allToolIcons={appMeta?.tool_icons || {}}
        onFeedback={handleFeedback}
        suggestedQuestions={suggestedQuestions}
        answerIcon={answerIcon}
        hideProcessDetail
        themeBuilder={themeBuilder}
        switchSibling={siblingMessageId => setTargetMessageId(siblingMessageId)}
        inputDisabled={inputDisabled}
        isMobile={isMobile}
        sidebarCollapseState={sidebarCollapseState}
        embedSource={embedSource}
        larkInfo={larkInfo}
        currentIntent={currentIntent}
        updateIntent={updateIntent}
        updateLlm={updateLlm}
        updateIntentSilent={updateIntentSilent}
        updateLastChatIntent={updateLastChatIntent}
      />
    </div>
  )
}

export default ChatWrapper
