import { forwardRef, useEffect, useImperativeHandle } from 'react'
import Uploader from './uploader'
import { useFiles } from './hooks'
import FileList from './file-list'
import { ImagePlus } from '@/app/components/base/icons/src/vender/line/images'
import { type ImageFile } from '@/types/app'

type IFileUploader = {
  onFilesChange: (imageFile: ImageFile) => void
}
const FileUploader = forwardRef<any, IFileUploader>(({ onFilesChange }, ref) => {
  const { files, onUpload, onRemove, onReUpload, onClear } = useFiles()

  useEffect(() => {
    onFilesChange?.(files?.[0])
  }, [files])

  useImperativeHandle(ref, () => ({
    onClear,
  }))

  return (
    <div className="flex items-center">
      {files.length
        ? (
          <FileList list={files} onRemove={onRemove} onReUpload={onReUpload} />
        )
        : (
          <Uploader onUpload={onUpload}>
            {hovering => (
              <div
                className={`
            flex items-center justify-center px-3 h-8 bg-gray-100
            text-xs text-gray-500 rounded-lg cursor-pointer
            ${hovering && 'bg-gray-200'}
          `}
              >
                <ImagePlus className="w-4 h-4 text-gray-500" />
                本地上传
              </div>
            )}
          </Uploader>
        )}
    </div>
  )
})

FileUploader.displayName = 'FileUploader'

export default FileUploader
