import { RiCloseLine, RiLoader2Line } from '@remixicon/react'
import { AlertTriangle } from '@/app/components/base/icons/src/vender/solid/alertsAndFeedback'
import { RefreshCcw01 } from '@/app/components/base/icons/src/vender/line/arrows'
import Tooltip from '@/app/components/base/tooltip'

type FileListProps = {
  list: any[]
  onRemove?: (id: string) => void
  onReUpload?: (id: string) => void
}

const FileList = ({ list, onRemove, onReUpload }: FileListProps) => {
  return (
    <ul>
      {list?.map((item, key) => (
        <li key={key} className="flex justify-between items-center">
          <p className="text-xs text-gray-500 mr-[4px]">{item.file.name}</p>
          {item.progress > -1 && item.progress !== 100 && (
            <RiLoader2Line className="animate-spin w-4 h-4 text-gray-500" />
          )}
          {item.progress === -1 && (
            <>
              <RefreshCcw01
                className="w-4 h-4 text-gray-500 cursor-pointer"
                onClick={() => onReUpload && onReUpload(item._id)}
              />
              <Tooltip popupContent={'文件上传失败'}>
                <AlertTriangle className="w-4 h-4 text-[#DC6803] mr-[4px]" />
              </Tooltip>
            </>
          )}
          <RiCloseLine
            className="w-4 h-4 text-gray-500 cursor-pointer ml-[4px]"
            onClick={() => onRemove && onRemove(item._id)}
          />
        </li>
      ))}
    </ul>
  )
}

export default FileList
