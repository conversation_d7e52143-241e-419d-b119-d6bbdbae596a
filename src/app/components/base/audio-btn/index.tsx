'use client'
import { useState } from 'react'
import { t } from 'i18next'
import { useParams, usePathname } from 'next/navigation'
import s from './style.module.css'
import Tooltip from '@/app/components/base/tooltip'
import Loading from '@/app/components/base/loading'
import { AudioPlayerManager } from '@/app/components/base/audio-btn/audio.player.manager'
import type { EmbedSource } from '@/models/share'

const LarkVoice = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <path d="M16.138029,3 C17.0372207,3.8385226 17.7550621,4.85238354 18.247273,5.9790507 C18.7394839,7.10571786 18.9957144,8.32133218 18.9999444,9.55082454 C19.0042645,10.7803169 18.756584,11.9975692 18.2722931,13.1277014 C17.7879121,14.2577435 17.0771808,15.2766355 16.1838391,16.1213771 M13.6828243,5.63288304 C14.2223753,6.135993 14.6530261,6.74431317 14.9484067,7.42031346 C15.2436973,8.09633175 15.3974176,8.82569315 15.4000276,9.56333456 C15.4025476,10.301066 15.2539573,11.0314174 14.9633467,11.7094787 C14.6727362,12.38754 14.2463154,12.9988211 13.7102743,13.5057021 M5.93227944,12.6483605 L7.43998932,14.514154 C8.22593383,15.4867859 8.61896458,15.9731468 8.96321524,16.0260669 C9.26129581,16.071877 9.56243639,15.9654068 9.76547677,15.7424764 C10.0000172,15.4848959 10.0000172,14.8595747 10.0000172,13.6091123 L10.0000172,5.55528489 C10.0000172,4.3047775 10.0000172,3.6795283 9.76547677,3.42195681 C9.56243639,3.19898138 9.26129581,3.09249318 8.96321524,3.13832126 C8.61896458,3.19126837 8.22593383,3.6775843 7.43998932,4.65021616 L5.93227944,6.51600973 C5.77350114,6.7124981 5.69411198,6.81074229 5.5958768,6.88143743 C5.50884663,6.94406855 5.41133144,6.99064364 5.30791224,7.01897569 C5.19119102,7.05094375 5.06487578,7.05094375 4.8122543,7.05094375 L3.53125484,7.05094375 C2.85067354,7.05094375 2.51038289,7.05094375 2.23537836,7.14178993 C1.69418033,7.32058427 1.26964052,7.74512408 1.09084617,8.28632212 C1,8.56136264 1,8.90165329 1,9.5822346 C1,10.2628159 1,10.6031066 1.09084617,10.8780571 C1.26964052,11.4193181 1.69418033,11.8438489 2.23537836,12.0225893 C2.51038289,12.1134894 2.85067354,12.1134894 3.53125484,12.1134894 L4.8122543,12.1134894 C5.06487578,12.1134894 5.19119102,12.1134894 5.30791224,12.1454395 C5.41133144,12.1737896 5.50884663,12.2203196 5.5958768,12.2829598 C5.69411198,12.3536099 5.77350114,12.4518901 5.93227944,12.6483605 Z" stroke="#A3AFBB" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd" />
  </svg>

)

const LarkPause = ({ className }: { className: string }) => (
  <svg className={className} fill="#3B67FF" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1447" width="200" height="200"><path d="M512 42.666667a469.333333 469.333333 0 1 0 469.333333 469.333333A469.333333 469.333333 0 0 0 512 42.666667z m0 864a394.666667 394.666667 0 1 1 394.666667-394.666667 395.146667 395.146667 0 0 1-394.666667 394.666667z" p-id="1448"></path><path d="M427.2 332.106667a37.333333 37.333333 0 0 0-37.333333 37.333333v285.12a37.333333 37.333333 0 0 0 74.666666 0V369.44a37.333333 37.333333 0 0 0-37.333333-37.333333zM596.8 332.106667a37.333333 37.333333 0 0 0-37.333333 37.333333v285.12a37.333333 37.333333 0 1 0 74.666666 0V369.44a37.333333 37.333333 0 0 0-37.333333-37.333333z" p-id="1449"></path></svg>
)

type AudioBtnProps = {
  id?: string
  voice?: string
  value?: string
  className?: string
  isAudition?: boolean
  noCache?: boolean
  embedSource?: EmbedSource
}

type AudioState = 'initial' | 'loading' | 'playing' | 'paused' | 'ended'

const AudioBtn = ({
  id,
  voice,
  value,
  className,
  isAudition,
  embedSource,
}: AudioBtnProps) => {
  const [audioState, setAudioState] = useState<AudioState>('initial')

  const params = useParams()
  const pathname = usePathname()
  const audio_finished_call = (event: string): void => {
    switch (event) {
      case 'ended':
        setAudioState('ended')
        break
      case 'paused':
        setAudioState('ended')
        break
      case 'loaded':
        setAudioState('loading')
        break
      case 'timeupdate':
      case 'play':
        setAudioState('playing')
        break
      case 'error':
        setAudioState('ended')
        break
    }
  }
  let url = ''
  let isPublic = false

  if (params.token) {
    url = '/text-to-audio'
    isPublic = true
  }
  else if (params.appId) {
    if (pathname.search('explore/installed') > -1)
      url = `/installed-apps/${params.appId}/text-to-audio`
    else
      url = `/apps/${params.appId}/text-to-audio`
  }
  const handleToggle = async () => {
    if (audioState === 'playing' || audioState === 'loading') {
      setTimeout(() => setAudioState('paused'), 1)
      AudioPlayerManager.getInstance().getAudioPlayer(url, isPublic, id, value, voice, audio_finished_call).pauseAudio()
    }
    else {
      setTimeout(() => setAudioState('loading'), 1)
      AudioPlayerManager.getInstance().getAudioPlayer(url, isPublic, id, value, voice, audio_finished_call).playAudio()
    }
  }

  const tooltipContent = {
    initial: t('appApi.play'),
    ended: t('appApi.play'),
    paused: t('appApi.pause'),
    playing: t('appApi.playing'),
    loading: t('appApi.loading'),
  }[audioState]

  return (
    <div className={`inline-flex items-center justify-center ${(audioState === 'loading' || audioState === 'playing') ? 'mr-1' : className}`}>
      <Tooltip
        popupContent={tooltipContent}
      >
        <button
          disabled={audioState === 'loading'}
          className={`box-border flex h-6 w-6 cursor-pointer items-center justify-center ${isAudition ? 'p-0.5' : 'rounded-md bg-white p-0'}`}
          onClick={handleToggle}
        >
          {audioState === 'loading'
            ? (
              <div className='flex h-full w-full items-center justify-center rounded-md'>
                <Loading />
              </div>
            )
            : (
              <div className={'flex h-full w-full items-center justify-center rounded-md hover:bg-gray-50'}>
                {embedSource && (audioState === 'playing' ? <LarkPause className='w-[20px] h-[20px]' /> : <LarkVoice className='w-[20px] h-[20px]'></LarkVoice>)}
                {!embedSource && <div className={`h-4 w-4 ${(audioState === 'playing') ? s.pauseIcon : s.playIcon}`}></div>}
              </div>
            )}
        </button>
      </Tooltip>
    </div>
  )
}

export default AudioBtn
