'use client'
import { useState } from 'react'
import { t } from 'i18next'
import { debounce } from 'lodash-es'
import copy from 'copy-to-clipboard'
import s from './style.module.css'
import Tooltip from '@/app/components/base/tooltip'
import Image from '@/app/components/base/image'
import { EmbedSource } from '@/models/share'

const LarkIcon = ({ className }: { className: string }) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" className={className}>
    <g stroke="currentColor" stroke-width="1.4" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd">
      <rect x="4" y="5.8991776" width="10" height="12" rx="1" />
      <path d="M7,5.8991776 L7,4 C7,3.44771525 7.44771525,3 8,3 L16,3 C16.5522847,3 17,3.44771525 17,4 L17,13.8991776 C17,14.4514624 16.5522847,14.8991776 16,14.8991776 L14,14.8991776 L14,14.8991776" />
    </g>
  </svg>
)

type ICopyBtnProps = {
  value: string
  className?: string
  isPlain?: boolean
  embedSource?: EmbedSource
}

const CopyBtn = ({
  value,
  className,
  isPlain,
  embedSource
}: ICopyBtnProps) => {
  const [isCopied, setIsCopied] = useState(false)

  const onClickCopy = debounce(() => {
    copy(value)
    setIsCopied(true)
  }, 100)

  const onMouseLeave = debounce(() => {
    setIsCopied(false)
  }, 100)

  return (
    <div className={`${className}`}>
      <Tooltip
        popupContent={(isCopied ? t('appApi.copied') : t('appApi.copy'))}
        asChild={false}
      >
        <div
          onMouseLeave={onMouseLeave}
          className={'box-border flex cursor-pointer items-center justify-center rounded-md bg-components-button-secondary-bg p-0.5'}
          style={!isPlain
            ? {
              boxShadow: '0px 4px 8px -2px rgba(16, 24, 40, 0.1), 0px 2px 4px -2px rgba(16, 24, 40, 0.06)',
            }
            : {}}
          onClick={onClickCopy}
        >
          {embedSource && <LarkIcon className={`w-[20px] h-[20px] rounded-md text-[#A3AFBB] hover:bg-gray-50`}></LarkIcon>}
          {!embedSource && <div className={`h-6 w-6 rounded-md hover:bg-components-button-secondary-bg-hover  ${s.copyIcon} ${isCopied ? s.copied : ''}`}></div>}
        </div>
      </Tooltip>
    </div>
  )
}

export default CopyBtn
