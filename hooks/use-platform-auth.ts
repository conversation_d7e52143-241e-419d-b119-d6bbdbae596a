import { getAccessToken, removeAccessToken } from '@/app/components/share/utils'
import { getQueryParam } from '@/utils'
import { fetchSsoRedirect } from '@/service/share'
import { EMBED_SOURCE_TYPE } from '@/config'
const usePlatformAuth = () => {
  /**
   * 判断是否需要平台认证
   * 此函数根据URL参数和当前访问状态决定是否需要重定向用户到认证平台进行认证
   * 主要逻辑包括：
   * 1. 从URL参数中提取嵌入来源（embedSource）和认证代码（code）
   * 2. 构造重定向URL以进行平台认证
   * 3. 根据不同的嵌入来源（如飞书、企业微信），构造不同的认证URL并重定向
   *
   *
   * @returns {boolean} 如果需要平台认证，则返回true；否则返回false
   */
  const needPlatformAuth = (type = false) => {
    return new Promise((resolve) => {
      // 获取URL参数
      const urlParams: any = getQueryParam()
      let embedSource: string = urlParams.get('state')
      if (process.env.NEXT_PUBLIC_OAUTH_NAME === 'CSCEC4A' && !embedSource)
        embedSource = EMBED_SOURCE_TYPE.ZJ4A

      const code: string = urlParams.get('code')
      // 构造重定向URL
      const sharedToken = globalThis.location.pathname.split('/').slice(-1)[0]
      const accessToken = getAccessToken() || { [sharedToken]: '' }
      const redirectUrl = location.origin + location.pathname
      const tokenInfo = accessToken[sharedToken]
      // 当前token与state不匹配
      const noEffectToken = embedSource && tokenInfo && tokenInfo.source !== embedSource

      // t_token：第三方平台携带token则忽略
      if((embedSource === EMBED_SOURCE_TYPE.TOKEN))
        return resolve(true)

      if (type || (embedSource && !tokenInfo && !code) || noEffectToken) {
        fetchSsoRedirect(sharedToken, embedSource).then((data: any) => {
          if (data && data.redirectFullUrl) {
            const url = decodeURIComponent(data.redirectFullUrl).replace('$redirectUri', redirectUrl)
            removeAccessToken()

            location.href = decodeURIComponent(url)
          }
          resolve(true)
        }).catch((err) => {
          resolve(err)
        })
      }
      else {
        resolve(false)
      }
    })
  }

  return {
    needPlatformAuth,
  }
}

export default usePlatformAuth
