import { menuPermissions } from '@/app/(commonLayout)/app/(appDetailLayout)/[appId]/layout-main'
import { useCheckPagePermission } from '@/app/components/authorize/authorize'
import { pathKey } from '@/app/components/authorize/permissions'
import { getRedirection as redirect } from '@/utils/app-redirection'


const useAppRedirect = () => {
    const { checkPermission } = useCheckPagePermission()

    const getRedirection = (
        app: any,
        redirectionFunc: (href: string) => void,
    ) => {
        // 有编排权限
        if (checkPermission(pathKey.configuration)) {
            redirect(app, redirectionFunc)
        } else {
            // 默认跳转到有权限的页面
            for (let [key, value] of Object.entries(menuPermissions)) {
                if (checkPermission(value)) {
                    redirectionFunc(value.replace(':id', app.id))
                    break;
                }
            }
        }
    }

    return {
        getRedirection
    }
}

export default useAppRedirect