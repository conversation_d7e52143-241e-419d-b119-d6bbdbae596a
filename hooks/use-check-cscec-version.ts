import { EMBED_SOURCE_TYPE } from '@/config'
import { getQueryParam } from '@/utils'

const useCheckCscecVersion = () => {
  function checkLanguageKeyword(ua: string) {
    // 1. 检查是否包含 Language 关键字
    const hasLanguage = /language\//i.test(ua)

    // 2. 提取 Language 后的值（如 zh_CN）
    const languageMatch = ua.match(/Language\/(\S+)/i)
    const languageValue = languageMatch ? languageMatch[1] : null

    return { hasLanguage, languageValue }
  }

  function getWXWorkVersion() {
    const ua = navigator.userAgent
    const version = ua.match(/wxworklocal\/([\d.]+)/)?.[1] || null
    return version
  }

  const needUpgrade = () => {
    const { hasLanguage } = checkLanguageKeyword(navigator.appVersion)
    const urlParams: any = getQueryParam()
    const embedSource: string = urlParams.get('state')

    return !hasLanguage && embedSource === EMBED_SOURCE_TYPE.ZJT && getWXWorkVersion()
  }

  return {
    needUpgrade,
  }
}

export default useCheckCscecVersion
