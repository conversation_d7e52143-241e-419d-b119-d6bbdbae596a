{"name": "aigc", "version": "1.6.0", "license": "BSD-3-<PERSON><PERSON>", "private": true, "dependencies": {"@arco-design/web-react": "^2.65.0", "@emoji-mart/data": "^1.2.1", "@floating-ui/react": "^0.27.13", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^1.8.3", "@remixicon/react": "^4.6.0", "@tailwindcss/typography": "^0.5.16", "@types/react-syntax-highlighter": "^15.5.13", "@volcengine/rtc": "4.66.1", "ahooks": "^3.9.0", "autoprefixer": "^10.4.21", "best-effort-json-parser": "^1.1.3", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dotenv-cli": "^8.0.0", "echarts-for-react": "^3.0.2", "emoji-mart": "^5.6.0", "i18next": "^25.3.0", "immer": "^10.1.1", "katex": "^0.16.22", "ky": "^1.8.1", "lodash-es": "^4.17.21", "lru-cache": "^11.1.0", "mermaid": "^11.7.0", "mime": "^4.0.7", "nanoid": "^5.1.5", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet": "^6.1.0", "react-hotkeys-hook": "^5.1.0", "react-i18next": "^15.5.3", "react-markdown": "^10.1.0", "react-redux": "^8.0.2", "react-router": "^6.3.0", "react-router-dom": "^6.3.0", "react-syntax-highlighter": "^15.6.1", "redux": "^4.2.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "swr": "^2.3.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "unist-util-visit": "^5.0.0", "use-context-selector": "^2.0.0", "uuid": "^8.3.2", "zustand": "^5.0.6"}, "scripts": {"dev": "npm run echo && npm run start", "start": "cross-env REACT_APP_LOCAL=cn  craco start", "server:start": "node Server/app.js", "build": "craco build", "test": "dotenv -e .env.test craco build", "eject": "react-scripts eject", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "eslint": "eslint  src/ --fix --cache --quiet --ext .js,.jsx,.ts,.tsx", "stylelint": "stylelint 'src/**/*.less' --fix", "pre-commit": "npm run eslint && npm run stylelint", "echo": "node message.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^6.4.5", "@types/lodash": "^4.17.4", "@types/node": "^16.11.45", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "@types/react-helmet": "^6.1.11", "@types/uuid": "^8.3.4", "craco-less": "^2.0.0", "cross-env": "^7.0.3", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-prettier": "^4.2.1", "postcss-less": "^6.0.0", "prettier": "^2.7.1", "react-scripts": "5.0.1", "stylelint": "^14.9.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^26.0.0", "typescript": "^4.7.4"}}